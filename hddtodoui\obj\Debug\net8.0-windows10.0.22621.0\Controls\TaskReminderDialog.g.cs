﻿#pragma checksum "D:\netProject\newhddtodoui\hddtodoUI\Controls\TaskReminderDialog.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "A72CC7B0C0BF2E63DA877AE3719F107A7926EBECF2E7E2C760F6E8F48B3B21FC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HddtodoUI.Controls
{
    partial class TaskReminderDialog : 
        global::Microsoft.UI.Xaml.Controls.ContentDialog, 
        global::Microsoft.UI.Xaml.Markup.IComponentConnector
    {

        /// <summary>
        /// Connect()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public void Connect(int connectionId, object target)
        {
            switch(connectionId)
            {
            case 1: // Controls\TaskReminderDialog.xaml line 2
                {
                    global::Microsoft.UI.Xaml.Controls.ContentDialog element1 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ContentDialog>(target);
                    ((global::Microsoft.UI.Xaml.Controls.ContentDialog)element1).CloseButtonClick += this.ContentDialog_CloseButtonClick;
                }
                break;
            case 2: // Controls\TaskReminderDialog.xaml line 46
                {
                    this.RepeatIntervalPanel = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.StackPanel>(target);
                }
                break;
            case 3: // Controls\TaskReminderDialog.xaml line 52
                {
                    this.NextReminderInfoTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 4: // Controls\TaskReminderDialog.xaml line 48
                {
                    this.RepeatIntervalNumberBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.NumberBox>(target);
                    ((global::Microsoft.UI.Xaml.Controls.NumberBox)this.RepeatIntervalNumberBox).ValueChanged += this.RepeatIntervalNumberBox_OnValueChanged;
                }
                break;
            case 5: // Controls\TaskReminderDialog.xaml line 35
                {
                    this.RepeatTypeComboBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ComboBox>(target);
                    ((global::Microsoft.UI.Xaml.Controls.ComboBox)this.RepeatTypeComboBox).SelectionChanged += this.RepeatTypeComboBox_SelectionChanged;
                }
                break;
            case 6: // Controls\TaskReminderDialog.xaml line 28
                {
                    this.ReminderDatePicker = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.DatePicker>(target);
                    ((global::Microsoft.UI.Xaml.Controls.DatePicker)this.ReminderDatePicker).SelectedDateChanged += this.ReminderDatePicker_OnSelectedDateChanged;
                }
                break;
            case 7: // Controls\TaskReminderDialog.xaml line 29
                {
                    this.ReminderTimePicker = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TimePicker>(target);
                    ((global::Microsoft.UI.Xaml.Controls.TimePicker)this.ReminderTimePicker).SelectedTimeChanged += this.ReminderTimePicker_OnSelectedTimeChanged;
                }
                break;
            default:
                break;
            }
            this._contentLoaded = true;
        }


        /// <summary>
        /// GetBindingConnector(int connectionId, object target)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target)
        {
            global::Microsoft.UI.Xaml.Markup.IComponentConnector returnValue = null;
            return returnValue;
        }
    }
}

