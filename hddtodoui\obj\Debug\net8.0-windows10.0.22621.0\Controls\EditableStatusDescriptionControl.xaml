﻿<?xml version="1.0" encoding="utf-8"?>
<UserControl x:ConnectionId='1'
    x:Class="HddtodoUI.Controls.EditableStatusDescriptionControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid>
        <!-- 显示模式 -->
        <Grid x:ConnectionId='2' x:Name="DisplayModeGrid" Visibility="Visible">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock x:ConnectionId='7' x:Name="StatusTextBlock" 
                       Grid.Column="0"
                                                                     
                       Style="{StaticResource CaptionTextStyle}"
                       Foreground="{ThemeResource UserInfoTextBrush}"
                       VerticalAlignment="Center"
                       TextWrapping="Wrap"
                       Margin="0,4"/>
            
            <Button x:ConnectionId='8' x:Name="EditButton" 
                    Grid.Column="1"
                    Content="&#xE70F;"
                    FontFamily="Segoe MDL2 Assets"
                    Style="{StaticResource TransparentButtonStyle}"
                    Foreground="{ThemeResource UserInfoTextBrush}"
                    VerticalAlignment="Center"
                    Margin="8,0,0,0"
                    Opacity="0"
                                            
                    ToolTipService.ToolTip="编辑状态描述"/>
        </Grid>
        
        <!-- 编辑模式 -->
        <Grid x:ConnectionId='3' x:Name="EditModeGrid" Visibility="Collapsed">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBox x:ConnectionId='4' x:Name="StatusTextBox"
                     Grid.Column="0"
                                                                   
                     PlaceholderText="输入当前状态描述..."
                     VerticalAlignment="Center"
                     Margin="0,4"
                                                    />
            
            <Button x:ConnectionId='5' x:Name="SaveButton" 
                    Grid.Column="1"
                    Content="&#xE74E;"
                    FontFamily="Segoe MDL2 Assets"
                    Style="{StaticResource TransparentButtonStyle}"
                    Foreground="{ThemeResource SystemColorHighlightColor}"
                    VerticalAlignment="Center"
                    Margin="8,0,4,0"
                                            
                    ToolTipService.ToolTip="保存"/>
            
            <Button x:ConnectionId='6' x:Name="CancelButton" 
                    Grid.Column="2"
                    Content="&#xE711;"
                    FontFamily="Segoe MDL2 Assets"
                    Style="{StaticResource TransparentButtonStyle}"
                    Foreground="{ThemeResource SystemColorGrayTextColor}"
                    VerticalAlignment="Center"
                    Margin="4,0,0,0"
                                              
                    ToolTipService.ToolTip="取消"/>
        </Grid>
    </Grid>
</UserControl>

