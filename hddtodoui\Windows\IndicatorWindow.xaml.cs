using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Timers;
using Windows.Graphics;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.UICoordinator;
using HddtodoUI.Utilities;
using HddtodoUI.Converters;
using LanguageExt;
using Microsoft.UI;
using Microsoft.UI.Dispatching;
using Microsoft.UI.Windowing;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Input;
using Microsoft.UI.Xaml.Media;
using TaskStatus = HddtodoUI.Models.TaskStatus;
using Microsoft.UI.Composition.SystemBackdrops;
using Microsoft.UI.Composition;
using WinRT;

namespace HddtodoUI.Windows
{
    /// <summary>
    /// An empty window that can be used on its own or navigated to within a Frame.
    /// </summary>
    public sealed partial class IndicatorWindow : Window
    {
        private readonly DispatcherQueue dispatcherQueue;

        private Timer tomatoTimer;

        public IndicatorWindow()
        {
            this.InitializeComponent();
            WindowsUtils.NoTitleBar(this);

            // Set window size
            WindowsUtils.ResizeWindow(this, 667, 78);

            // Set window to be topmost
            WindowsUtils.SetAlwayOnTop(this);
            WindowsUtils.setTopMostAndLeftCorner(this);

            WindowsUtils.CenterWindow(this, 200);

            // Get the parent Border and add events to it as well
            DragDropMoveWindowHelper dragDropMoveWindowHelper = new DragDropMoveWindowHelper(this);

            TitleTextBorder.PointerPressed += dragDropMoveWindowHelper.Border_PointerPressed;
            TitleTextBorder.PointerMoved += dragDropMoveWindowHelper.Border_PointerMoved;
            TitleTextBorder.PointerReleased += dragDropMoveWindowHelper.Border_PointerReleased;

            CountdownTextBorder.PointerPressed += dragDropMoveWindowHelper.Border_PointerPressed;
            CountdownTextBorder.PointerMoved += dragDropMoveWindowHelper.Border_PointerMoved;
            CountdownTextBorder.PointerReleased += dragDropMoveWindowHelper.Border_PointerReleased;

            StartButtonBorder.PointerPressed += dragDropMoveWindowHelper.Border_PointerPressed;
            StartButtonBorder.PointerMoved += dragDropMoveWindowHelper.Border_PointerMoved;
            StartButtonBorder.PointerReleased += dragDropMoveWindowHelper.Border_PointerReleased;

            TaskCheckBoxBorder.PointerPressed += dragDropMoveWindowHelper.Border_PointerPressed;
            TaskCheckBoxBorder.PointerMoved += dragDropMoveWindowHelper.Border_PointerMoved;
            TaskCheckBoxBorder.PointerReleased += dragDropMoveWindowHelper.Border_PointerReleased;

            InfoTitleTextBorder.PointerPressed += dragDropMoveWindowHelper.Border_PointerPressed;
            InfoTitleTextBorder.PointerMoved += dragDropMoveWindowHelper.Border_PointerMoved;
            InfoTitleTextBorder.PointerReleased += dragDropMoveWindowHelper.Border_PointerReleased;

            TaskCheckBox.Checked += TaskCheckBox_OnChecked;
            TaskCheckBox.Unchecked += TaskCheckBox_OnUnChecked;

            WindowsUtils.NoResizeWindow(this);

            dispatcherQueue = DispatcherQueue.GetForCurrentThread();

            tomatoTimer = new Timer(1000); // 间隔为1000毫秒（1秒）
            tomatoTimer.Elapsed += TomatoTimer_Elapsed; // 订阅 Elapsed 事件
            tomatoTimer.AutoReset = true;
            tomatoTimer.Enabled = true;

            // 添加右键菜单事件
            this.RootGrid.RightTapped += RootGrid_RightTapped;
            this.InfoRootGrid.RightTapped += InfoRootGridOnRightTapped;

            this.AppWindow.SetIcon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Assets/HddTodoIcon.ico"));

            this.Activated += (sender, args) =>
            {
                LogService.Instance.Debug("IndicatorWindow Activated");
                getCurrentTomatoTaskOption().IfSome(RefreshTaskUI);
             
            };

            // === Setup Desktop Acrylic Backdrop ===
            TrySetAcrylicBackdrop(0.7f,0.7f);
        }

        private DesktopAcrylicController _acrylicController;
        private SystemBackdropConfiguration _backdropConfiguration;

        /// <summary>
        /// Initialize DesktopAcrylicBackdrop with specified opacity.
        /// </summary>
        /// <param name="opacity">Tint opacity value 0-1</param>
        private void TrySetAcrylicBackdrop(float TintOpacity,float LuminosityOpacity)
        {
            // Ensure only once
            if (_acrylicController != null)
                return;

            // WindowsAppSDK 1.3+ provides DesktopAcrylicController
            if (DesktopAcrylicController.IsSupported())
            {
                // Dispatcher queue must be initialized for composition.
                WindowsSystemDispatcherQueueHelper.EnsureDispatcherQueue();

                _backdropConfiguration = new SystemBackdropConfiguration()
                {
                    IsInputActive = true,
                    Theme = SystemBackdropTheme.Default
                };

                _acrylicController = new DesktopAcrylicController();
                _acrylicController.TintOpacity = TintOpacity;
                _acrylicController.LuminosityOpacity = LuminosityOpacity;
                _acrylicController.TintColor = Colors.White;

                // Hook activated/deactivated for IsInputActive and theme changes if needed
                Activated += (s, e) =>
                {
                    if (e.WindowActivationState == WindowActivationState.Deactivated)
                    {
                        _backdropConfiguration.IsInputActive = true;
                    }
                    else
                    {
                        _backdropConfiguration.IsInputActive = false;
                    }
                };
                
                Closed += (s, e) => _acrylicController.Dispose();

                _acrylicController.AddSystemBackdropTarget(this.As<ICompositionSupportsSystemBackdrop>());
                _acrylicController.SetSystemBackdropConfiguration(_backdropConfiguration);
            }
        }

        /// <summary>
        /// Helper to initialize DispatcherQueue when using system backdrops.
        /// </summary>
        private sealed class WindowsSystemDispatcherQueueHelper
        {
            private static bool _isInitialized;

            public static void EnsureDispatcherQueue()
            {
                if (_isInitialized) return;

                // If a DispatcherQueue already exists, no action is needed.
                if (DispatcherQueue.GetForCurrentThread() != null)
                {
                    _isInitialized = true;
                    return;
                }

                DispatcherQueueOptions options = new DispatcherQueueOptions()
                {
                    apartmentType = 2,    // DQTYPE_THREAD_CURRENT
                    threadType = 2,       // DQTAT_COM_STA
                    dwSize = (uint)System.Runtime.InteropServices.Marshal.SizeOf<DispatcherQueueOptions>()
                };

                CreateDispatcherQueueController(options, out var _);
                _isInitialized = true;
            }

            [System.Runtime.InteropServices.StructLayout(System.Runtime.InteropServices.LayoutKind.Sequential)]
            private struct DispatcherQueueOptions
            {
                public uint dwSize;
                public int threadType;
                public int apartmentType;
            }

            [System.Runtime.InteropServices.DllImport("CoreMessaging.dll")]
            private static extern int CreateDispatcherQueueController(DispatcherQueueOptions options, out object dispatcherQueueController);
        }

        public TaskStatus Status { get; set; }

        private void TomatoTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            dispatcherQueue.TryEnqueue(() =>
            {
                getCurrentTomatoTaskOption().IfSome(x =>
                {
                    TaskUICoordinatorFactory.Instance(x.getTask()).TomatoClockTicked();
                });
            });
        }

        public void TomatoClockTicked()
        {
            refreshTimeLabel();
            WindowsUtils.GetAppWindow(this).MoveInZOrderAtTop();

            var presenter = WindowsUtils.getPresenter(this);
            if (presenter.State == OverlappedPresenterState.Minimized)
            {
                presenter.Restore();
            }
        }

        private void SetCompleteStateCheckbox(bool value)
        {
            TaskCheckBox.Unchecked -= TaskCheckBox_OnUnChecked;
            TaskCheckBox.Checked -= TaskCheckBox_OnChecked;
            TaskCheckBox.IsChecked = value;
            TaskCheckBox.Checked += TaskCheckBox_OnChecked;
            TaskCheckBox.Unchecked += TaskCheckBox_OnUnChecked;
        }

        public void RefreshTaskUI(TomatoTaskManager tomatoTaskManager)
        {
            
           
            TaskTitleTextBlock.Text = tomatoTaskManager.getTask().Title;

            refreshTimeLabel();

            Status = TaskStatusExtensions.GetTaskStatus(tomatoTaskManager.getTaskStatus());
            LogService.Instance.Debug("INDICATOR WINDOW RefreshTaskUI STATUS: " + Status);
            switch (tomatoTaskManager.getTaskStatus())
            {
                case TomatoTaskStaus.taskCompleted:

                    SetCompleteStateCheckbox(true);

                    //tomatoTimer.Enabled = false;
                    //tomatoTimer.AutoReset = false;

                    TaskTitleTextBlock.Foreground = new SolidColorBrush(Colors.Gray);
                    CountdownTextBlock.Foreground = new SolidColorBrush(Colors.Gray);
                    StartButton.IsEnabled = false;

                    setStartButtonIcon(TaskStatus.Completed);

                    if (ConfigSettingsUtils.GetTaskCompletedAutoSwitchToTaskSelectionConfig())
                        SwitchToSelectTaskUI();
                    else
                        SwitchToTaskRunningUI();

                    break;

                case TomatoTaskStaus.taskUnCompletedAndTomatoOngoing:

                    SwitchToTaskRunningUI();

                    SetCompleteStateCheckbox(false);

                    TaskTitleTextBlock.Foreground = new SolidColorBrush(Colors.Black);
                    CountdownTextBlock.Foreground = new SolidColorBrush(Colors.Black);
                    StartButton.IsEnabled = true;

                    setStartButtonIcon(TaskStatus.InProgress);

                    refreshTimeLabel();

                    break;

                default: /*  TomatoTaskStaus.taskUnCompleteAndNotTomatoStart */

                    SetCompleteStateCheckbox(false);

                    TaskTitleTextBlock.Foreground = new SolidColorBrush(Colors.Black);
                    CountdownTextBlock.Foreground = new SolidColorBrush(Colors.Black);
                    //tomatoTimer.Enabled = false;
                    StartButton.IsEnabled = true;

                    setStartButtonIcon(TaskStatus.NotStarted);
                    refreshTimeLabel();

                    if (ConfigSettingsUtils.getTaskPauseAutoSwitchToTaskSelectionConfig())
                        SwitchToSelectTaskUI();
                    else
                        SwitchToTaskRunningUI();

                    break;
            }

            // IndicatorWindow 不再直接控制 TaskStepWindow 显隐
        }

        private void SwitchToSelectTaskUI()
        {
            MainSplitView.IsPaneOpen = true;
            MainSplitView.OpenPaneLength = this.AppWindow.Size.Width;
        }

        private void SwitchToTaskRunningUI()
        {
            MainSplitView.IsPaneOpen = false;
        }

        public void setStartButtonIcon(TaskStatus uiStatus)
        {
            var fontIcon = StartButton.Content as FontIcon;
            if (fontIcon != null)
            {
                // 手动更新FontIcon
                string glyph =
                    (new TaskStatusIconConverter()).Convert(uiStatus, null, null, null).ToString();
                if (!string.IsNullOrEmpty(glyph))
                {
                    fontIcon.Glyph = glyph;
                }

                var color =
                    (new TaskStatusColorConverter()).Convert(uiStatus, null, null, null) as Brush;
                if (color != null)
                {
                    fontIcon.Foreground = color;
                    LogService.Instance.Debug("set font icon color:" + color.ToString());
                }
            }
        }

        public Option<TomatoTaskManager> getCurrentTomatoTaskOption()
        {
            return CurrentStatusHolder.getCurrentStatusHolder().getCurrentTomatoTaskOption();
        }

        private void refreshTimeLabel()
        {
            getCurrentTomatoTaskOption().IfSome(x =>
            {
                CountdownTextBlock.Text = x.GetRemainTimeString();
            });
           
        }

        private void CountdownTextBorder_OnDoubleTapped(object sender, DoubleTappedRoutedEventArgs e)
        {
            LogService.Instance.Debug("indicator window double tapped");
            
            if (TheUICoordinator.Instance.GetTaskDetailsWindow().Visible == false)
                getCurrentTomatoTaskOption().IfSome(x =>
                {
                    ShowTaskDetailWindow(x.getTask());
                });
            else
            {
                
                getCurrentTomatoTaskOption().IfSome(x =>
                {  var taskId = x.getTaskID();
                    if ( TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl().IsTheSameTask(taskId))
                        TheUICoordinator.Instance.HideTaskDetailsWindow();
                    else
                        ShowTaskDetailWindow(x.getTask());
                });
            }
        }
        
        private void TitleTextBorder_OnTapped(object sender, TappedRoutedEventArgs e)
        {
            CountdownTextBorder_OnDoubleTapped(sender, null);
        }

        private void InfoTitleTextBorder_OnTapped(object sender, TappedRoutedEventArgs e)
        {
            LogService.Instance.Debug("indicator window info border tapped");

            
                if (TheUICoordinator.Instance.GetMainWindow().Visible == false)
                    TheUICoordinator.Instance.ShowMainWindow();
                else
                {
                    TheUICoordinator.Instance.GetTaskDetailsWindow().AskForSaveBeforeSwitchOrClose();
                    TheUICoordinator.Instance.HideMainWindow();
                }
            
        }

        private void StartButton_OnClick(object sender, RoutedEventArgs e)
        {
            getCurrentTomatoTaskOption().IfSome(x =>
            {
                TaskUICoordinatorFactory.Instance(x.getTask()).StartOrPauseButtonClick(x);
            });
            
        }

        private void TaskCheckBox_OnChecked(object sender, RoutedEventArgs e)
        {
            getCurrentTomatoTaskOption().IfSome(x =>
            {
                TaskUICoordinatorFactory.Instance(x.getTask()).TaskSwitchComplete(x.getTask());
            });
            
        }

        private void TaskCheckBox_OnUnChecked(object sender, RoutedEventArgs e)
        {
            getCurrentTomatoTaskOption().IfSome(x =>
            {
                TaskUICoordinatorFactory.Instance(x.getTask()).TaskSwitchUnComplete(x.getTask());
            });
        }

        // 添加右键菜单处理方法
        private async void RootGrid_RightTapped(object sender, RightTappedRoutedEventArgs e)
        {
            getCurrentTomatoTaskOption().IfSome(x =>
            {
                // 创建右键菜单
                MenuFlyout menuFlyout = new MenuFlyout();

                MenuFlyoutItem currentTaskItem = new MenuFlyoutItem
                {
                    Text = "编辑当前任务",
                    IsEnabled = true,
                    Icon = new FontIcon { Glyph = "\uE70F" }, // 编辑图标
                    Tag = x.getTask()
                };
                currentTaskItem.Click += EditRecentTask_Click;
                menuFlyout.Items.Add(currentTaskItem);

                MenuFlyoutItem copyTitleItem = new MenuFlyoutItem
                {
                    Text = "复制任务标题",
                    Icon = new FontIcon { Glyph = "\uE8C8" } // 复制图标
                };
                copyTitleItem.Click += CopyTaskTitle_Click;
                menuFlyout.Items.Add(copyTitleItem);

                menuFlyout.Items.Add(new MenuFlyoutSeparator());

                MakeRecentTaskMenuItem(menuFlyout);

                // TaskStepWindow 显隐菜单项
                MenuFlyoutItem toggleStepWindowItem = new MenuFlyoutItem
                {
                    Text = TheUICoordinator.Instance.IsTaskStepWindowVisible ? "隐藏任务步骤" : "显示任务步骤"
                };
                toggleStepWindowItem.Click += (s, args2) =>
                {
                    TheUICoordinator.Instance.ToggleTaskStepWindow(x.getTask(), this.AppWindow);
                };
                menuFlyout.Items.Add(toggleStepWindowItem);

                menuFlyout.ShowAt(sender as UIElement, e.GetPosition(sender as UIElement));
                  
            });
           
        }

        public void PopulateTemporyIndicatorSubmenu(
            IList<MenuFlyoutItemBase> targetItemsCollection, RoutedEventHandler taskItemClickHandler
            )
        {
            // Add menu title for "最近执行的任务" (Recently Executed Tasks)
            MenuFlyoutItem titleItem = new MenuFlyoutItem
            {
                Text = "最近执行的任务",
                IsEnabled = false
            };
            targetItemsCollection.Add(titleItem);
            targetItemsCollection.Add(new MenuFlyoutSeparator());

            // Add "正在加载..." menu item
            MenuFlyoutItem loadingItem = new MenuFlyoutItem
            {
                Text = "正在加载...",
                IsEnabled = false
            };

            bool isLoaded = false;
            loadingItem.Loaded += (s, e) =>
            {
                if (!isLoaded)
                    PopulateRecentTasksSubmenu(targetItemsCollection, dispatcherQueue, taskItemClickHandler, loadingItem);
                isLoaded = true;
            };

            targetItemsCollection.Add(loadingItem);
        }

        private async void PopulateRecentTasksSubmenu(
            IList<MenuFlyoutItemBase> targetItemsCollection,
            DispatcherQueue dq,
            RoutedEventHandler taskItemClickHandler, MenuFlyoutItem loadingItem = null
            )
        {

            // Asynchronously load recent task data
            await Task.Run(() =>
            {
                LogService.Instance.Debug("starting to populate recent tasks submenu");

                // Get recently executed 10 tasks
                var recentTasks = StoreFactoryHolder.getTaskStore().getRecentRunningTask(10, UserInfoHolder.getUserId());

                dq.TryEnqueue(() =>
                {
                    if (loadingItem != null)
                    {
                        targetItemsCollection.Remove(loadingItem);
                    }

                    if (recentTasks != null && recentTasks.Any()) // Requires using System.Linq;
                    {
                        foreach (var task in recentTasks)
                        {
                            MenuFlyoutItem taskItem = new MenuFlyoutItem
                            {
                                Text = task.Title, // Assuming task has a Title property
                                Tag = task, // Optional: store task ID for click handling
                            };
                            taskItem.Click += taskItemClickHandler; // Example: Add click handler if tasks are clickable
                            targetItemsCollection.Add(taskItem);
                        }
                    }
                    else
                    {
                        MenuFlyoutItem noTasksItem = new MenuFlyoutItem
                        {
                            Text = "没有最近任务", // "No recent tasks"
                            IsEnabled = false
                        };
                        targetItemsCollection.Add(noTasksItem);
                    }
                });

                LogService.Instance.Debug("populate recent tasks submenu finished");
            });
        }

        private async void MakeRecentTaskMenuItem(MenuFlyout menuFlyout)
        {

            // Create the new main menu item which is a sub-menu
            MenuFlyoutSubItem editRecentTasksSubItem = new MenuFlyoutSubItem
            {
                Text = "编辑最近执行的任务" // "Edit Recently Completed Tasks"
            };

            PopulateTemporyIndicatorSubmenu(editRecentTasksSubItem.Items, EditRecentTask_Click);

            menuFlyout.Items.Add(editRecentTasksSubItem);

            MenuFlyoutSubItem restartRecentTasksSubItem = new MenuFlyoutSubItem
            {
                Text = "重开最近执行的任务" // "Edit Recently Completed Tasks"
            };

            PopulateTemporyIndicatorSubmenu(restartRecentTasksSubItem.Items, RestartRecentTask_Click);

            menuFlyout.Items.Add(restartRecentTasksSubItem);
        }

        private void InfoRootGridOnRightTapped(object sender, RightTappedRoutedEventArgs e)
        {
            MenuFlyout menuFlyout = new MenuFlyout();
            MakeRecentTaskMenuItem(menuFlyout);
            menuFlyout.ShowAt(sender as UIElement, e.GetPosition(sender as UIElement));
        }

        // 点击最近任务菜单项的处理方法
        private void EditRecentTask_Click(object sender, RoutedEventArgs e)
        {
            if (sender is MenuFlyoutItem menuItem && menuItem.Tag is TTask task)
            {
                ShowTaskDetailWindow(task);
            }
        }

        private void RestartRecentTask_Click(object sender, RoutedEventArgs e)
        {
            if (sender is MenuFlyoutItem menuItem && menuItem.Tag is TTask task)
            {
                var tm = TaskTomatoManagerFactory.GetTomatoTaskManager(task);
                TaskUICoordinatorFactory.Instance(tm.getTask()).StartOrPauseButtonClick(tm);
            }
        }

        private void ShowTaskDetailWindow(TTask task)
        {
            var taskList = StoreFactoryHolder.getTaskCategoryStore().GetTaskCategoryByKey(task.BelongToListKey, UserInfoHolder.getUserId());

            if (taskList != null)
            {
                // 创建任务视图对象
                var taskViewObject = TodoTaskViewObject.GetFrom(task, taskList);

                // 显示任务详情窗口
                TheUICoordinator.Instance.ShowTaskDetailsWindow(taskViewObject);
            }
        }

        // 复制任务标题到剪贴板的处理方法
        private void CopyTaskTitle_Click(object sender, RoutedEventArgs e)
        {
            getCurrentTomatoTaskOption().IfSome(x =>
            {
                string taskTitle = x.getTask().Title;
                ClipboardUtils.CopyToClipboard(taskTitle);
                // 显示通知
                NotificationService.Instance.ShowNotification($"已复制任务标题: {taskTitle} 到剪贴板", NotificationLevel.Info, "复制成功");
            });
          
        }

        private void MainWindowButton_OnClick(object sender, RoutedEventArgs e)
        {
            TheUICoordinator.Instance.ToggleMainWindow();
        }

      
    }
}