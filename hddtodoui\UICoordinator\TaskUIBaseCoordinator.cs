using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.Utilities;
using HddtodoUI.Windows;
using LanguageExt;
using LanguageExt.UnsafeValueAccess;

namespace HddtodoUI.UICoordinator;

public class TaskUIBaseCoordinator
{
    public void TomatoClockTicked()
    {
        //LogService.Instance.Debug("tomato manager clock ticked");
        
        Option<bool> opt = getCurrentTomatoTaskManagerOption().Map(tm => tm.clockTicked());
        
        //var statusChanged = getCurrentTomatoTaskManager().clockTicked();
        //LogService.Instance.Debug("indicator clock ticked");
        TheUICoordinator.Instance.GetIndicatorWindow().TomatoClockTicked();
        //LogService.Instance.Debug("detail window clock ticked");
        TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl().TomatoTicked();
        
        opt.IfSome(statusChanged =>
        {
            if (statusChanged)
            {
                TomatoClockStatusChanged();
            }
        });
        
    }

    public void TomatoClockStatusChanged()
    {
        getCurrentTomatoTaskManagerOption().IfSome(tm =>
        {
            TheUICoordinator.Instance.GetIndicatorWindow()?.RefreshTaskUI(tm);
        });
      
        TheUICoordinator.Instance.GetTaskDetailsWindow()?.GetTaskDetailsControl()?.UpdateTimerControls();
        
        getCurrentTomatoTaskManagerOption().IfSome(tm =>
        {
            TheUICoordinator.Instance.GetMainWindow()?.GetMainView()?.GetTaskPanelTaskItemControlByTaskID(tm.getTaskID())?.RefreshUI();
        });
       
        
        NotificationService.Instance.ShowNotification("任务状态已变更", NotificationLevel.Info, "任务状态已变更");
        SoundUtility.PlayNotificationSound();
    }
    
    protected Option<TomatoTaskManager> getCurrentTomatoTaskManagerOption()
    {
        return CurrentStatusHolder.getCurrentStatusHolder().getCurrentTomatoTaskOption();
    }
    
    protected bool IsCurrentTomatoTask(long taskId)
    {
        return CurrentStatusHolder.getCurrentStatusHolder().IsCurrentRunningTask(taskId);
    }

    protected bool IsCurrentTaskDetailWindowTask(long taskId)
    {
        if (TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl().getMyTaskTomatoManager() !=
            null)
            if (TheUICoordinator.Instance.GetTaskDetailsWindow().GetTaskDetailsControl().getMyTaskTomatoManager()
                    .getTaskID() ==
                taskId)
                return true;
        return false;
    }
    
  
    


}