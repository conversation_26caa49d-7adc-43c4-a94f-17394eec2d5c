<?xml version="1.0" encoding="utf-8"?>

<UserControl
    x:Class="HddtodoUI.Controls.TaskCategoryItemForPlanControl"
    x:Name="root"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:converters="using:HddtodoUI.Converters"
    xmlns:backend="using:HddtodoUI.BackendModels"
    mc:Ignorable="d">

    <UserControl.Resources>
        <converters:DateTimeConverter x:Key="DateTimeConverter" />
        <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
        <converters:DateTimeToBrushConverter x:Key="DateTimeToBrushConverter" />
        <converters:IntToVisibilityConverter x:Key="IntToVisibilityConverter" />

        <Style x:Key="ProjectTitleTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BodyTextBlockStyle}">
            <Setter Property="FontSize" Value="14" />
            <Setter Property="FontWeight" Value="SemiBold" />
        </Style>
    </UserControl.Resources>

    <Grid Name="RootGrid" Padding="12,8" Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
          CornerRadius="8" Tapped="RootGrid_Tapped">
        <Grid.ContextFlyout>
            <MenuFlyout>
                <MenuFlyoutItem Text="修改截至时间" Click="ChangeDueTimeMenuItem_Click"/>
                <MenuFlyoutItem Text="完成该项目" Click="CompleteProjectMenuItem_Click"/>
            </MenuFlyout>
        </Grid.ContextFlyout>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>

        <TextBlock Grid.Column="0"
                   Text="&#xe91F;"
                   Foreground="{ThemeResource SeparatorBrush}"
                   FontFamily="{StaticResource SymbolThemeFontFamily}"
                   FontSize="7"
                   VerticalAlignment="Center"
                   Margin="0,0,8,0" />
     
        <StackPanel Grid.Column="1" Orientation="Vertical" Margin="16,0,0,0" VerticalAlignment="Center" >
            <TextBlock x:Name="CategoryNameTextBlock"
           TextWrapping="Wrap"
           Text="{x:Bind Category.Name, Mode=OneWay}"
           Style="{StaticResource TitleTextStyle}"
            />
            <TextBlock Text="{x:Bind ParentPath, Mode=OneWay}"
                       Foreground="Gray"
                       FontSize="12"
                       Margin="0,2,0,0"
                       Visibility="{x:Bind ParentPathVisibility, Mode=OneWay}" />
            <TextBlock Foreground="Gray"
                       FontSize="12"
                       Margin="0,4,0,0"
                     
                       HorizontalAlignment="Left">
                <Run Text="未完成任务数: "/>
                <Run Text="{x:Bind TaskCount, Mode=OneWay}"/>
            </TextBlock>
            <TextBlock Foreground="Gray"
                       FontSize="13"
                       Margin="8,4,0,0"
                       FontWeight="Normal"
                       HorizontalAlignment="Left"
                       Visibility="{x:Bind CategoryCount, Converter={StaticResource IntToVisibilityConverter}}">
                <Run Text="{x:Bind CategoryCount, Mode=OneWay}"/>
                <Run Text="个子分类"/>
            </TextBlock>
        </StackPanel>
        <TextBlock Grid.Column="2"
                   Text="{x:Bind Category.CategoryDueTime, Mode=OneWay, Converter={StaticResource DateTimeConverter}}"
                   Margin="16,0,0,0" VerticalAlignment="Center"
                   Foreground="{Binding Category.CategoryDueTime, ElementName=root, Converter={StaticResource DateTimeToBrushConverter}}" />
    </Grid>
</UserControl>
