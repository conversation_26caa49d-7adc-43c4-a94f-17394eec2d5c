using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Windows.ApplicationModel.DataTransfer;
using Windows.System;
using Windows.UI.Xaml.Media;
using ABI.Microsoft.UI;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.BackendStore;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.BackendModels.TaskFactory;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.TaskTomatoManager.Constants;
using HddtodoUI.UICoordinator;
using HddtodoUI.Utilities;
using Microsoft.UI.Text;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Data;
using Microsoft.UI.Xaml.Input;
using Brush = Microsoft.UI.Xaml.Media.Brush;
using Colors = Microsoft.UI.Colors;
using SolidColorBrush = Microsoft.UI.Xaml.Media.SolidColorBrush;
using VisualTreeHelper = Microsoft.UI.Xaml.Media.VisualTreeHelper;

namespace HddtodoUI.Controls
{
    public sealed partial class TasksPanel : UserControl
    {
        private const int PageSize = 10; // 每页显示的任务数

        // 当前显示的任务类别
        private TaskCategoryViewObject _currentCategory;

        //private bool _isCompletedTasksVisible = false;
        
        private bool _isGroupedByCategory = false; // 跟踪是否处于分组模式
        List<TodoTaskViewObject> cTasks = null;
        
        private int _completeTaskCurrentPage = 0;
        private int _completeTaskTotalPages = 1;

        // 用于已完成分类的分页
        private int _completedCategoryCurrentPage = 0;
        private int _completedCategoryTotalPages = 0;


        // 用于颜色筛选
        private string _selectedColor = "";
        private ObservableCollection<TodoTaskViewObject> _allTasks;

        // 构造函数
        public TasksPanel()
        {
            this.InitializeComponent();
            InitializeCollections();
            InitializeColorComboBox();
        }

        // 属性
        private ObservableCollection<TodoTaskViewObject> Tasks { get; set; }
        private ObservableCollection<TodoTaskViewObject> CompletedTasks { get; set; }
        private ObservableCollection<TaskCategoryViewObject> CompletedTaskCategories { get; set; }
        private ObservableCollection<TaskCategoryWithCount> SubCategories { get; set; }

        // 统一的任务列表项集合，包含任务和子分类
        private ObservableCollection<TaskListItem> UnifiedTaskList { get; set; }

        // 分组后的数据源
        private CollectionViewSource UnifiedTaskListCVS { get; set; }

        public TaskCategoryViewObject CurrentCategory
        {
            get => _currentCategory;
            set
            {
                _currentCategory = value;
                UpdateCategoryDisplay();
                LoadSubCategories();
                UpdateUnifiedTaskList();
                // 重置已完成任务和已完成分类的分页
                _completeTaskCurrentPage = 0;
                _completedCategoryCurrentPage = 0;
                if (CompletedTasksExpander.IsExpanded)
                {
                    UpdateCompletedTasksView();
                }
                if (CompletedCategoryExpander.IsExpanded)
                {
                    UpdateCompletedCategoriesView();
                }
            }
        }

        // 事件定义，用于与 MainView 交互

        // 初始化集合
        private void InitializeCollections()
        {
            Tasks = new ObservableCollection<TodoTaskViewObject>();
            CompletedTasks = new ObservableCollection<TodoTaskViewObject>();
            CompletedTaskCategories = new ObservableCollection<TaskCategoryViewObject>();
            SubCategories = new ObservableCollection<TaskCategoryWithCount>();
            UnifiedTaskList = new ObservableCollection<TaskListItem>();

            // 创建CollectionViewSource用于分组
            UnifiedTaskListCVS = new CollectionViewSource
            {
                Source = UnifiedTaskList
            };

            // ItemsSource将在UpdateUnifiedTaskList()中设置
            // CompletedTasksListView is bound in XAML via x:Bind
            // CompletedCategoryListView is bound in XAML via x:Bind
            UpdateCompletedTasksView(); // Initial call might be needed if expander is initially open or for pre-loading
            //UpdateCompletedCategoriesView(); // Initial call

            // 注册任务项控件事件
            TaskListView.ItemContainerStyle = new Style(typeof(ListViewItem))
            {
                Setters =
                {
                    new Setter(HorizontalContentAlignmentProperty, HorizontalAlignment.Stretch)
                }
            };

            CompletedTasksListView.ItemContainerStyle = new Style(typeof(ListViewItem))
            {
                Setters =
                {
                    new Setter(HorizontalContentAlignmentProperty, HorizontalAlignment.Stretch)
                }
            };
            
            CompletedCategoryListView.ItemContainerStyle = new Style(typeof(ListViewItem))
            {
                Setters =
                {
                    new Setter(HorizontalContentAlignmentProperty, HorizontalAlignment.Stretch)
                }
            };
        }

        // 初始化颜色下拉框
        private void InitializeColorComboBox()
        {
            // 添加“所有颜色”选项
            ComboBoxItem allItem = new ComboBoxItem
            {
                Content = "所有颜色",
                Tag = ""
            };
            ColorComboBox.Items.Add(allItem);

            // 添加颜色选项
            AddColorItem("红色", "pink");
            AddColorItem("橙色", "orange");
            AddColorItem("绿色", "green");
            AddColorItem("蓝色", "blue");
            AddColorItem("紫色", "purple");
            AddColorItem("棕色", "brown");
            AddColorItem("金色", "gold");
            AddColorItem("黑色", "black");
            AddColorItem("橄榄色", "olive");
            AddColorItem("青色", "teal");
            AddColorItem("番茄色", "tomato");

            // 默认选中“所有颜色”
            ColorComboBox.SelectedIndex = 0;

            // 添加颜色选项改变事件处理
            ColorComboBox.SelectionChanged += ColorComboBox_SelectionChanged;
        }

        // 添加颜色选项到下拉框
        private void AddColorItem(string displayName, string colorName)
        {
            StackPanel panel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            // 颜色样本
            Border colorSample = new Border
            {
                Width = 16,
                Height = 16,
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(0, 0, 8, 0),
                Background = new SolidColorBrush(ColorUtils.ColorFromName(colorName))
            };

            // 文本
            TextBlock textBlock = new TextBlock
            {
                Text = displayName,
                VerticalAlignment = VerticalAlignment.Center
            };

            panel.Children.Add(colorSample);
            panel.Children.Add(textBlock);

            ComboBoxItem item = new ComboBoxItem
            {
                Content = panel,
                Tag = colorName
            };

            ColorComboBox.Items.Add(item);
        }

        // 颜色选项改变事件处理
        private void ColorComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ColorComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                _selectedColor = selectedItem.Tag as string ?? "";
                ApplyColorFilter();
            }
        }

        // 应用颜色过滤
        private void ApplyColorFilter()
        {
            if (_allTasks == null || _allTasks.Count == 0)
            {
                return;
            }

            // 如果没有选择颜色，则显示所有任务
            if (string.IsNullOrEmpty(_selectedColor))
            {
                Tasks.Clear();
                foreach (var task in _allTasks)
                {
                    Tasks.Add(task);
                }
            }
            else
            {
                // 过滤颜色
                Tasks.Clear();
                foreach (var task in _allTasks)
                {
                    if (!string.IsNullOrEmpty(task.Color) && task.Color.ToLower().Contains(_selectedColor + ":"))
                    {
                        Tasks.Add(task);
                    }
                }
            }

            // 如果是分组模式，则更新分组视图
            if (_isGroupedByCategory)
            {
                UpdateGroupedView();
            }
            else
            {
                // 更新统一任务列表
                UpdateUnifiedTaskList();
            }
        }

        // 更新类别显示
        private void UpdateCategoryDisplay()
        {
            if (CurrentCategory != null)
            {
                CategoryTitleTextBlock.Text = CurrentCategory.Name;
                // 可以在这里添加更多的类别相关显示逻辑，如颜色等
            }
        }

        // 加载子分类
        private void LoadSubCategories()
        {
            SubCategories.Clear();

            // 只有非系统分类才显示子分类
            if (CurrentCategory != null && !CurrentCategory.IsSystemCategory)
            {
                try
                {
                    var subCategories = StoreFactoryHolder.getTaskCategoryStore()
                        .GetUncompleteTaskCategoriesWithCount(UserInfoHolder.getUserId(), CurrentCategory.Key);

                    foreach (var subCategory in subCategories)
                    {
                        SubCategories.Add(subCategory);
                    }
                }
                catch (Exception ex)
                {
                    LogService.Instance.Error($"Error loading sub categories: {ex.Message} {ex.StackTrace}");
                }
            }
        }

        // 更新统一任务列表
        private void UpdateUnifiedTaskList()
        {
            UnifiedTaskList.Clear();

            // 如果是分组模式，使用分组逻辑
            if (_isGroupedByCategory)
            {
                // 计算每个task所属的category的orderpath
                foreach (var task in Tasks)
                {
                    if (task.Category != null)
                    {
                        List<string> orderParts = new List<string>();

                        var currentCategory = StoreFactoryHolder.getTaskCategoryStore()
                            .GetTaskCategoryByKey(task.Category, UserInfoHolder.getUserId());
                        while (currentCategory != null)
                        {
                            orderParts.Insert(0, currentCategory.CategoryOrder.ToString());
                            if ( currentCategory.ParentCategoryKey != null)
                                currentCategory = StoreFactoryHolder.getTaskCategoryStore()
                                    .GetTaskCategoryByKey(currentCategory.ParentCategoryKey, UserInfoHolder.getUserId());
                            else
                                currentCategory = null;
                        }
                        task.CategoryOrderPath = string.Join("-", orderParts);
                    }
                }

                // 按分类分组任务
                var groupedTasks = Tasks.GroupBy(task => new { task.CategoryName, task.CategoryOrderPath })
                    .OrderBy(group => group.Key.CategoryOrderPath)
                    .ToList();

                foreach (var group in groupedTasks)
                {
                    foreach (var task in group.OrderBy(t => t.TaskOrder))
                    {
                        var taskItem = TaskListItem.CreateTaskItem(task);
                        taskItem.GroupKey = group.Key.CategoryName ?? "未分类";
                        taskItem.GroupName = group.Key.CategoryName ?? "未分类";
                        UnifiedTaskList.Add(taskItem);
                    }
                }
            }
            else
            {
                // 添加任务项（分组：任务）
                foreach (var task in Tasks)
                {
                    UnifiedTaskList.Add(TaskListItem.CreateTaskItem(task));
                }

                // 添加子分类项（分组：子分类）
                foreach (var subCategory in SubCategories)
                {
                    UnifiedTaskList.Add(TaskListItem.CreateSubCategoryItem(subCategory));
                }
            }

            // 创建新的CollectionViewSource并设置分组
            UnifiedTaskListCVS.Source = UnifiedTaskList;

            // 检查是否需要显示分组标题
            bool shouldShowGroupHeaders = _isGroupedByCategory ||
                                        (!_isGroupedByCategory && UnifiedTaskList.GroupBy(item => item.GroupName).Count() > 1);

            if (shouldShowGroupHeaders && UnifiedTaskList.Count > 0)
            {
                // 创建分组数据 - 使用LINQ分组然后转换为CollectionViewSource期望的格式
                var groupedItems = UnifiedTaskList.GroupBy(item => item.GroupName)
                    .OrderBy(group => group.Key == "任务" ? 0 : 1) // 任务组在前，子分类组在后
                    .SelectMany(group => group.Prepend(null)) // 为每个组添加一个null作为组标题占位符
                    .Where(item => item != null)
                    .ToList();

                // 使用分组的CollectionViewSource
                var groupedSource = new CollectionViewSource
                {
                    Source = UnifiedTaskList.GroupBy(item => item.GroupName)
                        .OrderBy(group => group.Key == "任务" ? 0 : 1),
                    IsSourceGrouped = true
                };

                TaskListView.ItemsSource = groupedSource.View;
            }
            else
            {
                // 非分组模式或只有一个分组，直接显示
                TaskListView.ItemsSource = UnifiedTaskListCVS.View;
            }
        }

        // 公共方法：初始化任务
        public void InitializeTasks(IEnumerable<TodoTaskViewObject> activeTasks,
            IEnumerable<TodoTaskViewObject> activeViewObjectsTasks
        )
        {
            _allTasks = new ObservableCollection<TodoTaskViewObject>();
            foreach (var task in activeTasks)
            {
                _allTasks.Add(task);
            }

            Tasks.Clear();
            CompletedTasks.Clear();


            if (string.IsNullOrEmpty(_selectedColor))
            {
                foreach (var task in activeTasks)
                {
                    Tasks.Add(task);
                }
            }
            else
            {
                foreach (var task in activeTasks)
                {
                    if (!string.IsNullOrEmpty(task.Color) && task.Color.StartsWith(_selectedColor + ":"))
                    {
                        Tasks.Add(task);
                    }
                }
            }

            foreach (var task in activeViewObjectsTasks)
            {
                CompletedTasks.Add(task);
            }

            // TaskListView.ItemsSource = Tasks; // 现在使用统一列表
            UpdateUnifiedTaskList(); // 更新统一任务列表
            if (CompletedTasksExpander.IsExpanded)
                UpdateCompletedTasksView();
            
            if (CompletedCategoryExpander.IsExpanded)
                UpdateCompletedCategoriesView();
        }

        // 公共方法：设置当前类别的任务
        public void SetCategoryTasks(TaskCategoryViewObject category, IEnumerable<TodoTaskViewObject> activeTasks,
            IEnumerable<TodoTaskViewObject> completedTasks, // Renamed for clarity
            bool groupByCategory = false)
        {
            CurrentCategory = category;
            _isGroupedByCategory = groupByCategory && category.IsSystemCategory;

            _allTasks = new ObservableCollection<TodoTaskViewObject>();
            if (activeTasks != null)
            {
                foreach (var task in activeTasks)
                {
                    _allTasks.Add(task);
                }
            }

            if (_isGroupedByCategory)
            {
                Tasks.Clear();

                if (string.IsNullOrEmpty(_selectedColor))
                {
                    if (activeTasks != null)
                    {
                        foreach (var task in activeTasks)
                        {
                            Tasks.Add(task);
                        }
                    }
                }
                else
                {
                    if (activeTasks != null)
                    {
                        foreach (var task in activeTasks)
                        {
                            if (!string.IsNullOrEmpty(task.Color) && task.Color.StartsWith(_selectedColor + ":"))
                            {
                                Tasks.Add(task);
                            }
                        }
                    }
                }

                // 使用统一列表而不是直接设置ItemsSource
                UpdateUnifiedTaskList();

                if (CompletedTasksExpander.IsExpanded)
                    UpdateCompletedTasksView();
            }
            else
            {
                InitializeTasks(activeTasks, completedTasks);
            }
            
            if (category.IsSystemCategory && SpecialTaskListConstants.Deleted == category.Key)
            {
                TaskInputTextBox.IsEnabled = false;
            }
            else
            {
                TaskInputTextBox.IsEnabled = true;
            }

            if (category.IsSystemCategory)
            {
                CompletedCategoryExpander.Visibility = Visibility.Collapsed;
            }
            else
            {
                CheckToShowCompletedCategoryExpander(category.Key);
            }
        }

        public void CheckToShowCompletedCategoryExpander(string categoryKey)
        {
            int count = StoreFactoryHolder.getTaskCategoryStore()
                .GetCompletedTaskCategoriesCountAsync(UserInfoHolder.getUserId(), categoryKey).Count;
            if (count > 0)
                CompletedCategoryExpander.Visibility = Visibility.Visible;
            else
                CompletedCategoryExpander.Visibility = Visibility.Collapsed;
        }



        // 更新已完成任务视图
        private void UpdateCompletedTasksView()
        {
            // 初始化已完成任务
            CompletedTasks.Clear();

            if (_currentCategory == null) return;


            int ctasksCount = 0;


            if (!_currentCategory.IsSystemCategory)
            {
                var list = StoreFactoryHolder.getTaskCategoryStore().GetTaskCategoryByKey(_currentCategory.Key, UserInfoHolder.getUserId());

                ctasksCount = (int)StoreFactoryHolder.getTaskStore()
                    .getCompleteTasksCount(_currentCategory.Key, UserInfoHolder.getUserId());

                cTasks = StoreFactoryHolder.getTaskStore()
                    .getPagedCompleteTasks(_currentCategory.Key, UserInfoHolder.getUserId(), _completeTaskCurrentPage, PageSize)
                    .Select(t => TodoTaskViewObject.GetFrom(t, list)).ToList();
                
                _completeTaskTotalPages = (int)(ctasksCount + PageSize - 1) / PageSize -1;
                if (ctasksCount == 0) _completeTaskTotalPages = 0;

                _completeTaskCurrentPage = Math.Max(0, Math.Min(_completeTaskCurrentPage, _completeTaskTotalPages));
            }
            else
            {
                cTasks = StoreFactoryHolder.getTaskStore().getTodayCompleteTasks(UserInfoHolder.getUserId()).Select(t =>
                {
                    var list = StoreFactoryHolder.getTaskCategoryStore().GetTaskCategoryByKey(t.BelongToListKey, UserInfoHolder.getUserId());
                    return TodoTaskViewObject.GetFrom(t, list);
                }).ToList();
                ctasksCount = cTasks.Count();

                _completeTaskTotalPages = 0;
                _completeTaskCurrentPage = 0;
            }


            LogService.Instance.Debug("CurrentPage count: " + _completeTaskCurrentPage);
            LogService.Instance.Debug("cTasks count: " + ctasksCount);

            RefreshCompletedTasksView();
        }

        public void RefreshTaskListTask(TTask task)
        {
            var before = _allTasks.FirstOrDefault(t => t.TaskID == task.TaskID);
            var beforeIndex = _allTasks.IndexOf(before);
            _allTasks.Remove(before);

            var list = StoreFactoryHolder.getTaskCategoryStore().GetTaskCategoryByKey(task.BelongToListKey,UserInfoHolder.getUserId());
            _allTasks.Insert(beforeIndex, TodoTaskViewObject.GetFrom(task,list));

        }

        private void RefreshCompletedTasksView()
        {
            try
            {
                CompletedTasksListView.ItemsSource = null;

                // 按完成日期分组
                var groupedTasks = from task in cTasks
                    group task by GetCompletionDateString(task.CompletedDate)
                    into g
                    select g;

                var cvs = new CollectionViewSource
                {
                    Source = groupedTasks,
                    IsSourceGrouped = true
                };

                CompletedTasksListView.ItemsSource = cvs.View;

                // 更新分页控件
                UpdatePaginationControls();

                // 根据 _isCompletedTasksVisible 显示或隐藏已完成任务
                CompletedTasksListView.Visibility = Visibility.Visible;
                   // _isCompletedTasksVisible ? Visibility.Visible : Visibility.Collapsed;


                // if (CompletedTasksListView.Visibility == Visibility.Visible)
                // {
                //     var bg = new SolidColorBrush(
                //         Colors.LightGray)
                //     {
                //         Opacity = 0.15
                //     };
                //     CompletedTasksButtonBorder.Background = bg;
                //     CompletedTasksListView.Background = bg;
                // }
                // else
                // {
                //     var tbg = new SolidColorBrush(
                //         Colors.Transparent
                //     );
                //     CompletedTasksButtonBorder.Background = tbg;
                //     CompletedTasksListView.Background = tbg;
                // }
            }
            catch (Exception e)
            {
                LogService.Instance.Error(e.StackTrace);
                throw;
            }
        }

        // 更新分页控件
        private void UpdatePaginationControls()
        {
            PageInfoText.Text = $"第 {_completeTaskCurrentPage + 1} 页 / 共 {_completeTaskTotalPages + 1} 页";
            PreviousPageButton.IsEnabled = _completeTaskCurrentPage > 0;
            NextPageButton.IsEnabled = _completeTaskCurrentPage < _completeTaskTotalPages;

            var paginationVisibility = _completeTaskTotalPages > 0 ? Visibility.Visible : Visibility.Collapsed;
            PreviousPageButton.Visibility = paginationVisibility;
            PageInfoText.Visibility = paginationVisibility;
            NextPageButton.Visibility = paginationVisibility;
        }

        // 事件处理程序：上一页按钮点击
        private void PreviousPage_Click(object sender, RoutedEventArgs e)
        {
            if (_completeTaskCurrentPage > 0)
            {
                _completeTaskCurrentPage--;
                UpdateCompletedTasksView();
            }
        }

        // 事件处理程序：下一页按钮点击
        private void NextPage_Click(object sender, RoutedEventArgs e)
        {
            if (_completeTaskCurrentPage < _completeTaskTotalPages)
            {
                _completeTaskCurrentPage++;
                UpdateCompletedTasksView();
            }
        }

       
        private void CompletedTasksExpander_OnExpanding(Expander sender, ExpanderExpandingEventArgs args)
        {
            _completeTaskCurrentPage = 0; // Reset to first page on expand
            UpdateCompletedTasksView();
            
            var textBlock = FindChild<TextBlock>(sender, "CompletedTasksExpanderTextBlock");
            if (textBlock != null)
            {
                textBlock.Opacity = 1.0;
                textBlock.FontWeight = FontWeights.Bold;
               
            }
        }
        
        private void CompletedTasksExpander_OnCollapsed(Expander sender, ExpanderCollapsedEventArgs args)
        {
            var textBlock = FindChild<TextBlock>(sender, "CompletedTasksExpanderTextBlock");
            if (textBlock != null)
            {
                textBlock.Opacity = 0.5;
                textBlock.FontWeight = FontWeights.Normal;
               
             
            }
        }
        
        // --- Completed Categories Logic ---
        private void CompletedCategoryExpander_OnExpanding(Expander sender, ExpanderExpandingEventArgs args)
        {
            _completedCategoryCurrentPage = 0; // Reset to first page
            UpdateCompletedCategoriesView();

            // Find the TextBlock in the HeaderTemplate and change its opacity
            var textBlock = FindChild<TextBlock>(sender, "CompletedCategoryExpanderTextBlock");
            if (textBlock != null)
            {
                textBlock.Opacity = 1.0;
                textBlock.FontWeight = FontWeights.Bold;
               
            }
        }
        
     

        private void CompletedCategoryExpander_OnCollapsed(Expander sender, ExpanderCollapsedEventArgs args)
        {
            var textBlock = FindChild<TextBlock>(sender, "CompletedCategoryExpanderTextBlock");
            if (textBlock != null)
            {
                textBlock.Opacity = 0.5;
                textBlock.FontWeight = FontWeights.Normal;
               
            }
            
        }

        private void UpdateCompletedCategoriesView()
        {
            CompletedTaskCategories.Clear();
            if (!CurrentStatusHolder.getCurrentStatusHolder().isCurrentASpecialList())
            {
                var parentKey = CurrentStatusHolder.getCurrentStatusHolder().getCurrentTaskList().Key;
                try
                {
                    var ret =   StoreFactoryHolder.getTaskCategoryStore().GetCompletedTaskCategoriesCountAsync(UserInfoHolder.getUserId(),parentKey);
                    long totalCount = ret.Count;
                    _completedCategoryTotalPages = (int)(totalCount + PageSize - 1) / PageSize -1;
                    if (totalCount == 0) _completedCategoryTotalPages = 0;

                    _completedCategoryCurrentPage = Math.Max(0, Math.Min(_completedCategoryCurrentPage, _completedCategoryTotalPages));

                    var categories =  StoreFactoryHolder.getTaskCategoryStore().GetCompletedTaskCategoriesAsync(UserInfoHolder.getUserId(),parentKey,null,_completedCategoryCurrentPage,PageSize);
                
                    foreach (var cat in categories)
                    {
                        CompletedTaskCategories.Add(TaskCategoryViewObject.GetFrom(cat));
                    }
                }
                catch (Exception ex)
                {
                    LogService.Instance.Error($"Error loading completed categories: {ex.Message} {ex.StackTrace}");
                    // Optionally display an error to the user
                }
                finally
                {
                    RefreshCompletedCategoriesView();
                }
            }
           
        }

        private void RefreshCompletedCategoriesView()
        {
            // CompletedCategoryListView.ItemsSource is already bound to CompletedTaskCategories via x:Bind
            // So, just need to update pagination and visibility
            CompletedCategoryListView.ItemsSource = CompletedTaskCategories;
            UpdateCategoryPaginationControls();
            CompletedCategoryListView.Visibility = CompletedTaskCategories.Any() || _completedCategoryTotalPages > 0 ? Visibility.Visible : Visibility.Collapsed;
        }


        private void UpdateCategoryPaginationControls()
        {
            CategoryPageInfoText.Text = $"第 {_completedCategoryCurrentPage + 1} 页 / 共 {_completedCategoryTotalPages + 1} 页";
            PreviousCategoryPageButton.IsEnabled = _completedCategoryCurrentPage > 0;
            NextCategoryPageButton.IsEnabled = _completedCategoryCurrentPage < _completedCategoryTotalPages;

            var paginationVisibility = _completedCategoryTotalPages > 0 ? Visibility.Visible : Visibility.Collapsed; // Show if more than one page
            PreviousCategoryPageButton.Visibility = paginationVisibility;
            CategoryPageInfoText.Visibility = paginationVisibility;
            NextCategoryPageButton.Visibility = paginationVisibility;
        }

        private void PreviousCategoryPage_Click(object sender, RoutedEventArgs e)
        {
            if (_completedCategoryCurrentPage > 0)
            {
                _completedCategoryCurrentPage--;
                 UpdateCompletedCategoriesView();
            }
        }

        private void NextCategoryPage_Click(object sender, RoutedEventArgs e)
        {
            if (_completedCategoryCurrentPage < _completedCategoryTotalPages)
            {
                _completedCategoryCurrentPage++;
                 UpdateCompletedCategoriesView();
            }
        }
        // --- End Completed Categories Logic ---


        private string GetCompletionDateString(DateTime? date)
        {
            if (!date.HasValue) return "未知日期";
            if (date.Value.Date == DateTime.Today) return "今天";
            if (date.Value.Date == DateTime.Today.AddDays(-1)) return "昨天";
            return date.Value.ToString("yyyy-MM-dd");
        }

        private void UpdateGroupedView()
        {
            double pos = ScrollUtils.SaveScrollPosition(TaskListView);
            // 使用统一列表方法更新视图
            UpdateUnifiedTaskList();
            ScrollUtils.RestoreScrollPosition(TaskListView, pos);
        }

        private void TaskInputTextBox_KeyDown(object sender, KeyRoutedEventArgs e)
        {
            if (e.Key == VirtualKey.Enter && !string.IsNullOrWhiteSpace(TaskInputTextBox.Text))
            {
                AddNewTask();
                e.Handled = true;
            }
        }

        private void AddNewTask()
        {
            if (string.IsNullOrWhiteSpace(TaskInputTextBox.Text))
                return;


            TTask thisTask = null;
            TaskCategory currentTaskList = CurrentStatusHolder.getCurrentStatusHolder().getCurrentTaskList();

            if (CurrentStatusHolder.getCurrentStatusHolder().isCurrentASpecialList())
            {
                if (SpecialTaskListConstants.Deleted ==
                    CurrentStatusHolder.getCurrentStatusHolder().getCurrentSpecialListName())
                    return;

                thisTask = SystemCategoryManager.CreateTaskBySystemCategoryName(TaskInputTextBox.Text, CurrentStatusHolder.getCurrentStatusHolder().getCurrentSpecialListName());
                
            }


            if (thisTask == null)
                thisTask = TTaskFactory.CreateTaskInTaskList(TaskInputTextBox.Text, currentTaskList);

            TaskUICoordinatorFactory.Instance(thisTask).OnTaskAdded(thisTask, currentTaskList);

            TaskInputTextBox.Text = string.Empty;
        }

        //拖拽处理-------------------------------------------------------------------------------------

        private Dictionary<TodoTaskViewObject, int> _originalIndices;

        private List<TodoTaskViewObject> _draggedItems;

        // 拖拽开始事件处理
        private void TaskListView_DragItemsStarting(object sender, DragItemsStartingEventArgs e)
        {
            LogService.Instance.Debug("拖拽开始");
            TheUICoordinator.Instance.SetTaskCategoryListViewCanotReOrder();

            // 检查拖拽的项目是否为任务项
            if (e.Items.Count > 0 && e.Items[0] is TaskListItem taskListItem)
            {
                // 只允许拖拽任务项，不允许拖拽子分类项
                if (taskListItem.Type != TaskListItem.ItemType.Task)
                {
                    e.Cancel = true;
                    return;
                }

                var draggedTask = taskListItem.Task;

                // 只允许在同一个状态内的任务之间拖拽
                bool isCompleted = draggedTask.IsCompleted;
                var listView = sender as ListView;

                e.Data.SetText(draggedTask.Title);
                e.Data.RequestedOperation = DataPackageOperation.Move;
                // 可选：记录源 ListView
                e.Data.Properties.Add("SourceListView", sender as ListView);
                e.Data.Properties.Add("Item",draggedTask);

                // 获取所有可见的任务项（从UnifiedTaskList中获取，避免分组数据结构的问题）
                var taskItems = UnifiedTaskList.Where(item => item.Type == TaskListItem.ItemType.Task);
                var items = taskItems.Select(item => item.Task);

                _draggedItems = e.Items.Cast<TaskListItem>()
                    .Where(item => item.Type == TaskListItem.ItemType.Task)
                    .Select(item => item.Task)
                    .ToList();
                _originalIndices = new Dictionary<TodoTaskViewObject, int>();

                foreach (var item in _draggedItems)
                {
                    int originalIndex = Tasks.IndexOf(item);
                    _originalIndices[item] = originalIndex;
                }

                LogService.Instance.Info("拖拽任务数量:" + _draggedItems.Count);

                // 如果拖拽的是已完成任务，但目标位置有未完成任务，或反之，则取消拖拽
                if (items.Any(task => task.IsCompleted != isCompleted))
                {
                    e.Cancel = true;
                }
            }
        }

        // 拖拽完成事件处理
        private void TaskListView_DragItemsCompleted(ListViewBase sender, DragItemsCompletedEventArgs args)
        {
            LogService.Instance.Debug("拖拽完成");

            if (args.DropResult == DataPackageOperation.Move)
            {
                foreach (var draggedItem in _draggedItems)
                {
                    int originalIndex = _originalIndices[draggedItem];

                    // 从UnifiedTaskList中找到对应的TaskListItem
                    var taskListItem = UnifiedTaskList.FirstOrDefault(item =>
                        item.Type == TaskListItem.ItemType.Task &&
                        item.Task?.TaskID == draggedItem.TaskID);

                    if (taskListItem == null)
                    {
                        LogService.Instance.Error($"无法在UnifiedTaskList中找到任务: {draggedItem.Title}");
                        continue;
                    }

                    // 获取在UnifiedTaskList中的索引
                    int unifiedListIndex = UnifiedTaskList.IndexOf(taskListItem);

                    // 计算在任务序列中的新位置（排除子分类项）
                    var taskItems = UnifiedTaskList.Where(item => item.Type == TaskListItem.ItemType.Task).ToList();
                    int newIndex = taskItems.IndexOf(taskListItem);

                    // 同时检查在Tasks集合中的位置，确保数据一致性
                    int tasksIndex = Tasks.IndexOf(draggedItem);

                    if (newIndex == originalIndex)
                    {
                        // 位置没有变化，更新统一列表以确保UI同步
                        UpdateUnifiedTaskList();
                        return;
                    }

                    if (tasksIndex != -1) // 仍在源列表中，说明是内部重新排序
                    {
                        LogService.Instance.Debug(
                            $"内部排序: '{draggedItem.Title}' 从索引 {originalIndex} 移动到 {newIndex}");

                        // 使用taskItems列表来计算目标项，确保索引正确
                        if (newIndex >= 0 && newIndex < taskItems.Count)
                        {
                            TodoTaskViewObject targetItem = null;

                            if (newIndex > originalIndex && newIndex > 0)
                            {
                                // 向下拖：目标是新位置的前一个任务
                                targetItem = taskItems[newIndex - 1].Task;
                            }
                            else if (newIndex < originalIndex && newIndex < taskItems.Count - 1)
                            {
                                // 向上拖：目标是新位置的后一个任务
                                targetItem = taskItems[newIndex + 1].Task;
                            }

                            if (targetItem != null)
                            {
                                LogService.Instance.Info($"推断的目标项: {targetItem.Title}");
                                LogService.Instance.Debug($"推断的目标项: {targetItem.Title}");

                                StoreFactoryHolder.getTaskStore().dragUncompleteTask(_draggedItems[0].TaskID,
                                    targetItem.TaskID, UserInfoHolder.getUserId());
                            }
                            else
                            {
                                LogService.Instance.Error($"无法确定拖拽目标项，原始索引: {originalIndex}, 新索引: {newIndex}");
                            }
                        }

                        // 更新统一任务列表以保持UI同步
                        UpdateUnifiedTaskList();

                        TheUICoordinator.Instance.RefreshTaskPanelUncompletedTaskListView();
                    }
                }
            }

            TheUICoordinator.Instance.SetTaskCategoryListViewCanReOrder();
            // 重置临时变量
            _draggedItems = null;
            _originalIndices = null;
        }


        // -------------------------------------------------------------------------
        

        public TodoTaskViewObject GetTaskFromCategoryTop(long taskId)
        {
            var from = Tasks.FirstOrDefault(t => t.TaskID == taskId);

            //从from位置往上查找，直到找到不是同类别的任务或者到顶部为止
            int targetIndex = 0;
            for (int i = 0; i <= Tasks.IndexOf(from); i++)
            {
                var item = Tasks[i];
                if (item.Category == from.Category)
                {
                    targetIndex = i;
                    break;
                }
            }

            return Tasks[targetIndex];
        }

        public TodoTaskViewObject GetTaskFromCategoryBottom(long taskId)
        {
            var from = Tasks.FirstOrDefault(t => t.TaskID == taskId);

            int curIndex = Tasks.IndexOf(from);
            int targetIndex = curIndex;
            for (int i = (Tasks.Count - 1); i >= curIndex; i--)
            {
                var item = Tasks[i];
                if (item.Category == from.Category)
                {
                    targetIndex = i;
                    break;
                }
            }

            return Tasks[targetIndex];
        }

        


        private void SetIncompleteButton_Click(object sender, RoutedEventArgs e)
        {
            var button = e.OriginalSource as Button;
           var taskCategory = button.Tag as TaskCategoryViewObject;
           LogService.Instance.Debug(taskCategory.Name);
           
           
           var parentCategory = StoreFactoryHolder.getTaskCategoryStore()
               .GetTaskCategoryByKey(taskCategory.ParentCategoryKey, UserInfoHolder.getUserId());

           var order = StoreFactoryHolder.getTaskCategoryStore()
               .GetUncompleteTaskCategoriesWithCount(UserInfoHolder.getUserId(), parentCategory.Key).Count() + 1;

           
           StoreFactoryHolder.getTaskCategoryStore().UpdateTaskCategory(taskCategory.Key,
               UserInfoHolder.getUserId(), UpdateValue<DateTime?>.SetTo(null),null,taskCategory.Name,taskCategory.IsDeleted,order);

           CompletedTaskCategories.Remove(taskCategory);
           
           TheUICoordinator.Instance.OnTaskCategoryUnCompleted(parentCategory.Key, taskCategory);

        }

        // Helper method to find a child element in the visual tree
        public static T FindChild<T>(DependencyObject parent, string childName) where T : FrameworkElement
        {
            // Confirm parent and childName are valid.
            if (parent == null) return null;

            T foundChild = null;

            int childrenCount = VisualTreeHelper.GetChildrenCount(parent);
            for (int i = 0; i < childrenCount; i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                // If the child is not of the request child type child
                T childType = child as T;
                if (childType == null)
                {
                    // recursively drill down the tree
                    foundChild = FindChild<T>(child, childName);

                    // If the child is found, break so we do not overwrite the found child.
                    if (foundChild != null) break;
                }
                else if (!string.IsNullOrEmpty(childName))
                {
                    var frameworkElement = child as FrameworkElement;
                    // If the child's name is set for search
                    if (frameworkElement != null && frameworkElement.Name == childName)
                    {
                        // if the child's name is of the request name
                        foundChild = (T)child;
                        break;
                    }
                    // recursively drill down the tree
                    foundChild = FindChild<T>(child, childName);
                    if (foundChild != null) break;
                }
                else
                {
                    // child element found.
                    foundChild = (T)child;
                    break;
                }
            }
            return foundChild;
        }

      
    }
}