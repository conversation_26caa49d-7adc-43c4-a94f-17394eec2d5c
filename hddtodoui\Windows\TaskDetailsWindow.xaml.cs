using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using Windows.Devices.SmartCards;
using Windows.Graphics;
using HddtodoUI.Controls;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.UICoordinator;
using HddtodoUI.Utilities;
using Microsoft.UI;
using Microsoft.UI.Windowing;
using Microsoft.UI.Xaml.Controls;
using WinRT.Interop;
using Window = Microsoft.UI.Xaml.Window;

namespace HddtodoUI.Windows
{
    public sealed partial class TaskDetailsWindow : Window
    {
        
        public TaskDetailsWindow()
        {
            this.InitializeComponent();

            // Get the window handle
            var _appWindow = WindowsUtils.GetAppWindow(this);

            // Get the presenter
            var _presenter = _appWindow.Presenter as OverlappedPresenter;
            if (_presenter != null)
            {
                _presenter.IsAlwaysOnTop = false;
                _presenter.IsResizable = true;
                _presenter.IsMaximizable = true;
                _presenter.IsMinimizable = true;
            }

            // Set window size and position
            InitializeWindowPositionAndSize();
           
            this.AppWindow.SetIcon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Assets/HddTodoIcon.ico"));

            // Setup events
            TaskDetailsControl.BackRequested += TaskDetailsControl_BackRequested;

            _appWindow.Closing += AppWindowOnClosing;
            //_appWindow.Changed += AppWindowOnChanged;
            
        }

        private void AppWindowOnClosing(AppWindow sender, AppWindowClosingEventArgs args)
        {
            // 保存窗口位置和大小
            SaveWindowPositionAndSize();
            
            GetTaskDetailsControl().AskSaveOrDismissBeforeClose();
            
            CurrentStatusHolder.getCurrentStatusHolder().getCurrentTomatoTaskOption().IfSome(x =>
            {
                TheUICoordinator.Instance.UpdateTaskStepWindow(x.getTask(), false);
            });
           
            //await AskForSwitchOrClose();

            // 无未保存修改：取消默认关闭，转为隐藏窗口并交由 UICoordinator 处理 TaskStepWindow 恢复
            // args.Cancel = false;
            // TheUICoordinator.Instance.HideTaskDetailsWindow();
        }
        
     


        public void AskForSaveBeforeSwitchOrClose()
        {
              GetTaskDetailsControl().AskSaveOrDismissBeforeClose();
            
        }
        
        public AppWindow FromParent { set; get; } 
        
        private void TaskDetailsControl_BackRequested(object sender, EventArgs e)
        {
            TheUICoordinator.Instance.DetailWindowShowFromParentWindow();
        }

        public void ShowAndActivate(TodoTaskViewObject taskViewObject = null)
        {
            if (taskViewObject != null)
            {
                TaskDetailsControl.CheckModifiedAndLoadTask(taskViewObject);
            }


            this.Activate();
            // Bring window to foreground
            //SetForegroundWindow(_windowHandle);
            this.AppWindow.MoveInZOrderAtTop();
        }

        public TaskDetailsControl GetTaskDetailsControl()
        {
            return TaskDetailsControl;
        }

        // private void AppWindowOnChanged(AppWindow sender, AppWindowChangedEventArgs args)
        // {
        //     // 当窗口位置或大小改变时，如果启用了记住位置功能，则保存当前位置
        //     if (ConfigSettingsUtils.GetRememberTaskDetailsWindowPositionConfig())
        //     {
        //         SaveWindowPositionAndSize();
        //     }
        // }

        private void InitializeWindowPositionAndSize()
        {
            if (ConfigSettingsUtils.GetRememberTaskDetailsWindowPositionConfig())
            {
                // 尝试恢复保存的窗口位置和大小
                var savedX = ConfigSettingsUtils.GetTaskDetailsWindowX();
                var savedY = ConfigSettingsUtils.GetTaskDetailsWindowY();
                var savedWidth = ConfigSettingsUtils.GetTaskDetailsWindowWidth();
                var savedHeight = ConfigSettingsUtils.GetTaskDetailsWindowHeight();

                if (savedX != int.MinValue && savedY != int.MinValue)
                {
                    // 恢复窗口位置和大小（允许负值坐标，支持多显示器环境）
                    this.AppWindow.MoveAndResize(new RectInt32(savedX, savedY, savedWidth, savedHeight));
                }
                else
                {
                    // 如果没有保存的位置信息，使用默认大小并居中显示
                    WindowsUtils.ResizeWindow(this, savedWidth, savedHeight);
                    WindowsUtils.CenterWindow(this);
                }
            }
            else
            {
                // 如果未启用记住位置功能，使用默认设置
                WindowsUtils.ResizeWindow(this, 1280, 1000);
                WindowsUtils.CenterWindow(this);
            }
        }

        private void SaveWindowPositionAndSize()
        {
            if (ConfigSettingsUtils.GetRememberTaskDetailsWindowPositionConfig())
            {
                var position = this.AppWindow.Position;
                var size = this.AppWindow.Size;

                if (WindowsUtils.IsMinimized(this)) return;
                if (WindowsUtils.IsMaximized(this)) return;

                if (position.X == -32000 || position.Y == -32000) return;
                
                ConfigSettingsUtils.SetTaskDetailsWindowX(position.X);
                ConfigSettingsUtils.SetTaskDetailsWindowY(position.Y);
                ConfigSettingsUtils.SetTaskDetailsWindowWidth(size.Width);
                ConfigSettingsUtils.SetTaskDetailsWindowHeight(size.Height);

                // 保存屏幕信息（可选，用于多屏幕环境）
                try
                {
                    var displayArea = Microsoft.UI.Windowing.DisplayArea.GetFromWindowId(this.AppWindow.Id, Microsoft.UI.Windowing.DisplayAreaFallback.Primary);
                    if (displayArea != null)
                    {
                        var screenInfo = $"{displayArea.WorkArea.Width}x{displayArea.WorkArea.Height}";
                        ConfigSettingsUtils.SetTaskDetailsWindowScreen(screenInfo);
                    }
                }
                catch
                {
                    // 忽略屏幕信息获取失败的情况
                }
            }
        }
    }
}