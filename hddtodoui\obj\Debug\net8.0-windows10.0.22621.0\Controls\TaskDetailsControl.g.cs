﻿#pragma checksum "D:\netProject\newhddtodoui\hddtodoUI\Controls\TaskDetailsControl.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "4CBC738EBDDE349855E9D48BE14D27027E6923423438837F5919C62B24205D53"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace HddtodoUI.Controls
{
    partial class TaskDetailsControl : 
        global::Microsoft.UI.Xaml.Controls.UserControl, 
        global::Microsoft.UI.Xaml.Markup.IComponentConnector
    {

        /// <summary>
        /// Connect()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public void Connect(int connectionId, object target)
        {
            switch(connectionId)
            {
            case 2: // Controls\TaskDetailsControl.xaml line 300
                {
                    this.BottomBarGrid = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Grid>(target);
                }
                break;
            case 3: // Controls\TaskDetailsControl.xaml line 305
                {
                    this.SaveButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.SaveButton).Click += this.SaveButton_Click;
                }
                break;
            case 4: // Controls\TaskDetailsControl.xaml line 295
                {
                    this.TaskStepsControl = global::WinRT.CastExtensions.As<global::HddtodoUI.Controls.TaskStepsControl>(target);
                }
                break;
            case 5: // Controls\TaskDetailsControl.xaml line 286
                {
                    this.RestartInfoTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 6: // Controls\TaskDetailsControl.xaml line 287
                {
                    this.SetRestartButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.SetRestartButton).Click += this.SetRestartButton_Click;
                }
                break;
            case 7: // Controls\TaskDetailsControl.xaml line 275
                {
                    this.ReminderInfoTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 8: // Controls\TaskDetailsControl.xaml line 276
                {
                    this.SetReminderButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.SetReminderButton).Click += this.SetReminderButton_Click;
                }
                break;
            case 9: // Controls\TaskDetailsControl.xaml line 264
                {
                    this.CreatedDateTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                }
                break;
            case 10: // Controls\TaskDetailsControl.xaml line 254
                {
                    this.CategoryComboBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ComboBox>(target);
                }
                break;
            case 11: // Controls\TaskDetailsControl.xaml line 240
                {
                    this.PriorityComboBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.ComboBox>(target);
                }
                break;
            case 12: // Controls\TaskDetailsControl.xaml line 228
                {
                    this.DueDatePicker = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.CalendarDatePicker>(target);
                }
                break;
            case 13: // Controls\TaskDetailsControl.xaml line 175
                {
                    this.NotesGrid = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Grid>(target);
                }
                break;
            case 14: // Controls\TaskDetailsControl.xaml line 186
                {
                    this.NotesTextBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBox>(target);
                }
                break;
            case 15: // Controls\TaskDetailsControl.xaml line 162
                {
                    this.StartButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.StartButton).Click += this.StartButton_Click;
                }
                break;
            case 16: // Controls\TaskDetailsControl.xaml line 151
                {
                    this.TimerTextBlock = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBlock>(target);
                    ((global::Microsoft.UI.Xaml.Controls.TextBlock)this.TimerTextBlock).DoubleTapped += this.TimerTextBlock_DoubleTapped;
                }
                break;
            case 17: // Controls\TaskDetailsControl.xaml line 127
                {
                    this.TaskCompletionCheckBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.CheckBox>(target);
                    ((global::Microsoft.UI.Xaml.Controls.CheckBox)this.TaskCompletionCheckBox).Checked += this.TaskCompletionCheckBox_Checked;
                    ((global::Microsoft.UI.Xaml.Controls.CheckBox)this.TaskCompletionCheckBox).Unchecked += this.TaskCompletionCheckBox_Unchecked;
                }
                break;
            case 18: // Controls\TaskDetailsControl.xaml line 135
                {
                    this.TaskTitleTextBox = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.TextBox>(target);
                }
                break;
            case 19: // Controls\TaskDetailsControl.xaml line 87
                {
                    this.BackButton = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Button>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Button)this.BackButton).Click += this.BackButton_Click;
                }
                break;
            case 20: // Controls\TaskDetailsControl.xaml line 97
                {
                    this.IndicatorIcon = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.FontIcon>(target);
                }
                break;
            case 21: // Controls\TaskDetailsControl.xaml line 101
                {
                    this.BreadcrumbBar1 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.BreadcrumbBar>(target);
                }
                break;
            case 24: // Controls\TaskDetailsControl.xaml line 107
                {
                    global::Microsoft.UI.Xaml.Controls.Primitives.ToggleButton element24 = global::WinRT.CastExtensions.As<global::Microsoft.UI.Xaml.Controls.Primitives.ToggleButton>(target);
                    ((global::Microsoft.UI.Xaml.Controls.Primitives.ToggleButton)element24).Click += this.BreadcrumbBarItem_Click;
                }
                break;
            default:
                break;
            }
            this._contentLoaded = true;
        }


        /// <summary>
        /// GetBindingConnector(int connectionId, object target)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2503")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IComponentConnector GetBindingConnector(int connectionId, object target)
        {
            global::Microsoft.UI.Xaml.Markup.IComponentConnector returnValue = null;
            return returnValue;
        }
    }
}

