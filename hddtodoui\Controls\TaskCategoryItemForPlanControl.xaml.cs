using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using HddtodoUI.BackendModels;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using HddtodoUI.BackendModels.BackendStore;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.UICoordinator;
using HddtodoUI.Utilities;
using Microsoft.UI;
using Microsoft.UI.Input;
using Microsoft.UI.Xaml.Input;
using Microsoft.UI.Xaml.Media;

namespace HddtodoUI.Controls
{
    public sealed partial class TaskCategoryItemForPlanControl : UserControl, INotifyPropertyChanged
    {
        private ContentDialog _changeDueTimeDialog;
        private CalendarDatePicker _dueDatePicker;
        
        public TaskCategoryItemForPlanControl()
        {
            this.InitializeComponent();
            this.Loaded += TaskCategoryItemForPlanControl_Loaded;
            
            this.PointerEntered += TaskCategoryItemForPlanControl_PointerEntered;
            this.PointerExited += TaskCategoryItemForPlanControl_PointerExited;
        }

        public static readonly DependencyProperty CategoryProperty =
            DependencyProperty.Register("Category", typeof(TaskCategory), typeof(TaskCategoryItemForPlanControl), new PropertyMetadata(null, OnCategoryChanged));


        public TaskCategory Category
        {
            get { return (TaskCategory)GetValue(CategoryProperty); }
            set { SetValue(CategoryProperty, value); }
        }

        public static readonly DependencyProperty CategoryCountProperty =
            DependencyProperty.Register("CategoryCount", typeof(int), typeof(TaskCategoryItemForPlanControl), new PropertyMetadata(0));

        public int CategoryCount
        {
            get { return (int)GetValue(CategoryCountProperty); }
            set { SetValue(CategoryCountProperty, value); }
        }

        public static readonly DependencyProperty TaskCountProperty =
            DependencyProperty.Register("TaskCount", typeof(int), typeof(TaskCategoryItemForPlanControl), new PropertyMetadata(0));

        public int TaskCount
        {
            get { return (int)GetValue(TaskCountProperty); }
            set { SetValue(TaskCountProperty, value); }
        }

        public static readonly DependencyProperty ShowParentPathProperty =
            DependencyProperty.Register("ShowParentPath", typeof(bool), typeof(TaskCategoryItemForPlanControl), new PropertyMetadata(true));

        public bool ShowParentPath
        {
            get { return (bool)GetValue(ShowParentPathProperty); }
            set {
                SetValue(ShowParentPathProperty, value);
                OnPropertyChanged(nameof(ParentPathVisibility));
            }
        }

        public Visibility ParentPathVisibility
        {
            get
            {
                return (ShowParentPath && !string.IsNullOrEmpty(ParentPath))
                    ? Visibility.Visible
                    : Visibility.Collapsed;
            }
        }

        private string _parentPath;
        public string ParentPath
        {
            get => _parentPath;
            private set
            {
                if (_parentPath != value)
                {
                    _parentPath = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(ParentPathVisibility));
                }
            }
        }

        private void TaskCategoryItemForPlanControl_Loaded(object sender, RoutedEventArgs e)
        {
            UpdateParentPathAsync();
        }

        private static void OnCategoryChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is TaskCategoryItemForPlanControl control)
            {
                control.UpdateParentPathAsync();
            }
        }

        private void UpdateParentPathAsync()
        {
            if (Category == null || string.IsNullOrEmpty(Category.ParentCategoryKey))
            {
                ParentPath = string.Empty;
                return;
            }

            try
            {
                ParentPath =
                    TaskCategpryParentPathHelper.GetParentPathString(Category.ParentCategoryKey, Category.UserId);
            }
            catch
            {
                ParentPath = string.Empty;
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        private void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void RootGrid_Tapped(object sender, TappedRoutedEventArgs e)
        {
            try
            {
              
                if (Category != null)
                {
                     TheUICoordinator.Instance.TaskUserCategorySelected(TaskCategoryViewObject.GetFrom(Category));
                    TheUICoordinator.Instance.TaskCategorySwitchToUserCategory(TaskCategoryViewObject.GetFrom(Category));
                    
                }
            }
            catch (Exception ex)
            {
                LogService.Instance.Error(ex.Message, ex);
            }
        }


        private void TaskCategoryItemForPlanControl_PointerEntered(object sender, PointerRoutedEventArgs e)
        {
            //背景变为使用Microsoft.UI.Colors.LightGray的0.5透明度
            RootGrid.Background = new SolidColorBrush(
                Colors.LightGray)
            {
                Opacity = 0.2
            };

            this.ProtectedCursor = InputSystemCursor.Create(InputSystemCursorShape.Hand);
        }

        private void TaskCategoryItemForPlanControl_PointerExited(object sender, PointerRoutedEventArgs e)
        {
            RootGrid.Background = new SolidColorBrush(
                Colors.Transparent);
            this.ProtectedCursor = InputSystemCursor.Create(InputSystemCursorShape.Arrow);
        }

        // 右键菜单：修改截至时间
        private async void ChangeDueTimeMenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (Category == null) return;

            _dueDatePicker = new CalendarDatePicker
            {
                Date = Category.CategoryDueTime,
                MinDate = DateTimeOffset.Now.AddYears(-10),
                MaxDate = DateTimeOffset.Now.AddYears(10),
                PlaceholderText = "选择新的截至日期"
            };

            _changeDueTimeDialog = new ContentDialog
            {
                Title = "修改截至时间",
                Content = _dueDatePicker,
                PrimaryButtonText = "确定",
                CloseButtonText = "取消",
                XamlRoot = this.XamlRoot
            };

            var result = await _changeDueTimeDialog.ShowAsync();
            if (result == ContentDialogResult.Primary )
            {
                DateTime? newDueTime = null;
                
                if(_dueDatePicker.Date != null)
                {
                    newDueTime =  _dueDatePicker.Date.Value.DateTime;;
                }
                
                if (Category.CategoryDueTime != newDueTime)
                {
                    // 这里如有需要可调用后端保存逻辑
                    await StoreFactoryHolder.getTaskCategoryStore().UpdateTaskCategory(Category.Key, Category.UserId, UpdateValue<DateTime?>.NoChange,UpdateValue<DateTime?>.SetTo(newDueTime));
                    Category =  StoreFactoryHolder.getTaskCategoryStore().GetTaskCategoryByKey(Category.Key, Category.UserId);
                    OnPropertyChanged(nameof(Category));
                    TheUICoordinator.Instance.UpdateUserCategory(Category.Key, TaskCategoryViewObject.GetFrom(Category));
                    
                    await TheUICoordinator.Instance.ReloadPlannedProjectPanelAsync();
                }
            }
        }

        private async void CompleteProjectMenuItem_Click(object sender, RoutedEventArgs e)
        {
            // 这里写“完成改项目”的逻辑，以下为示例弹窗
            var dialog = new ContentDialog
            {
                Title = "提示",
                Content = $"确定要将项目“{Category?.Name}”标记为已完成吗？",
                PrimaryButtonText = "确定",
                CloseButtonText = "取消",
                XamlRoot = this.XamlRoot
            };
            var ret = await dialog.ShowAsync();

            if (ret == ContentDialogResult.Primary)
            {
                await StoreFactoryHolder.getTaskCategoryStore().UpdateTaskCategory(Category.Key, Category.UserId, UpdateValue<DateTime?>.SetTo(DateTime.Now));
                Category =  StoreFactoryHolder.getTaskCategoryStore().GetTaskCategoryByKey(Category.Key, Category.UserId);
                OnPropertyChanged(nameof(Category));

                await TheUICoordinator.Instance.ReloadPlannedProjectPanelAsync();
                TheUICoordinator.Instance.RemoveUserTaskCategoryFromPanel(TaskCategoryViewObject.GetFrom(Category));
                TheUICoordinator.Instance.UpdateSystemCategory();
            }


        }
      
    }
}
