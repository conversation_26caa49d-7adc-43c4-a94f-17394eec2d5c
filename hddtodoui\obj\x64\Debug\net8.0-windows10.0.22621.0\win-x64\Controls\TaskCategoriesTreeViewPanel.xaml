﻿<?xml version="1.0" encoding="utf-8"?>

<UserControl x:ConnectionId='1'
    x:Class="HddtodoUI.Controls.TaskCategoriesTreeViewPanel"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:converters="using:HddtodoUI.Converters"
    xmlns:models="using:HddtodoUI.Models"
    mc:Ignorable="d">

    <UserControl.Resources>
        <converters:DateTimeConverter x:Key="DateTimeConverter" />
        <converters:DueDateColorConverter x:Key="DueDateColorConverter" />
        <converters:InboxVisibilityConverter x:Key="InboxVisibilityConverter" />
        <converters:HasDateVisibilityConverter x:Key="HasDateVisibilityConverter" />
        <converters:TreeNodeGlyphConverter x:Key="TreeNodeGlyphConverter" />

    </UserControl.Resources>

    <Grid Background="{ThemeResource SidebarBackgroundBrush}" Padding="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" /> <!-- System Lists -->
            <RowDefinition Height="Auto" /> <!-- Separator -->
            <RowDefinition Height="*" /> <!-- User Lists -->
            <RowDefinition Height="Auto" /> <!-- Separator -->
            <RowDefinition Height="Auto" /> <!-- Add List Button -->
        </Grid.RowDefinitions>

        <!-- System Lists -->
        <ListView x:ConnectionId='2' x:Name="SystemTasksListView" SelectionMode="Single" 
                                                                   >
            <ListView.ItemTemplate>
                <DataTemplate>
                    <ListViewItem Style="{StaticResource TaskListItemStyle}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="30" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <FontIcon Grid.Column="0" Glyph="{Binding IconName}" FontSize="18" />
                            <!-- <FontIcon Grid.Column="0" Glyph="&#xE70B;"></FontIcon> -->
                            <TextBlock Grid.Column="1" Text="{Binding Name}" Style="{StaticResource BodyTextStyle}"
                                       TextWrapping="Wrap" />
                            <TextBlock Grid.Column="2" Text="{Binding TaskCount}"
                                       Style="{StaticResource CaptionTextStyle}"
                                       Margin="12,0,0,0" />
                        </Grid>
                    </ListViewItem>
                </DataTemplate>
            </ListView.ItemTemplate>
        </ListView>

        <!-- Separator -->

        <StackPanel Grid.Row="1" Margin="0,0,0,10">

            <Rectangle Height="1" Margin="0,28,0,12"
                       Fill="{ThemeResource SeparatorBrush}" />
            <FontIcon x:ConnectionId='52' Glyph="&#xE70E;" FontSize="8" Name="TopHint" Visibility="Collapsed" />
        </StackPanel>

        <!-- User Categories -->
        <ScrollViewer x:ConnectionId='3' Grid.Row="2" VerticalScrollBarVisibility="Hidden" HorizontalScrollBarVisibility="Disabled"
                                                                                                 >
            <TreeView x:ConnectionId='11' x:Name="UserCategoriesTreeView" SelectionMode="Single"
                                                                                
                                                                                    
                                                                                      
                                                                  
                                                                    
                                                                          
                                                          
                                                                    
                                                                  
                                                                    
                                                                      
                      AllowDrop="True"
                      CanReorderItems="True">
                <TreeView.ItemContainerStyle>
                    <Style TargetType="TreeViewItem">
                        <Setter Property="Padding" Value="0,0,0,0" />
                        <Setter Property="Margin" Value="0,0,0,0" />
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="TreeViewItem">
                                    <Grid x:Name="ContentPresenterGrid" Background="{TemplateBinding Background}"
                                          BorderBrush="{TemplateBinding BorderBrush}"
                                          BorderThickness="{TemplateBinding BorderThickness}"
                                          Margin="{ThemeResource TreeViewItemPresenterMargin}"
                                          Padding="{ThemeResource TreeViewItemPresenterPadding}"
                                          CornerRadius="{TemplateBinding CornerRadius}">
                                        <VisualStateManager.VisualStateGroups>
                                            <VisualStateGroup x:Name="CommonStates">
                                                <VisualState x:Name="Normal" />
                                                <VisualState x:Name="PointerOver">
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenterGrid.Background"
                                                                Value="{ThemeResource TreeViewItemBackgroundPointerOver}" />
                                                        <Setter Target="ContentPresenter.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundPointerOver}" />
                                                        <Setter Target="SelectionIndicator.Fill"
                                                                Value="{ThemeResource TreeViewItemSelectionIndicatorForegroundPointerOver}" />
                                                        <Setter Target="CollapsedGlyph.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundPointerOver}" />
                                                        <Setter Target="ExpandedGlyph.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundPointerOver}" />
                                                        <Setter Target="ContentPresenterGrid.BorderBrush"
                                                                Value="{ThemeResource TreeViewItemBorderBrushPointerOver}" />
                                                        <Setter Target="SelectionIndicator.Opacity" Value="0" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Pressed">
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenterGrid.Background"
                                                                Value="{ThemeResource TreeViewItemBackgroundPressed}" />
                                                        <Setter Target="ContentPresenter.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundPressed}" />
                                                        <Setter Target="SelectionIndicator.Fill"
                                                                Value="{ThemeResource TreeViewItemSelectionIndicatorForegroundPressed}" />
                                                        <Setter Target="CollapsedGlyph.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundPressed}" />
                                                        <Setter Target="ExpandedGlyph.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundPressed}" />
                                                        <Setter Target="ContentPresenterGrid.BorderBrush"
                                                                Value="{ThemeResource TreeViewItemBorderBrushPressed}" />
                                                        <Setter Target="SelectionIndicator.Opacity" Value="0" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Selected">
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenterGrid.Background"
                                                                Value="{ThemeResource TreeViewItemBackgroundSelected}" />
                                                        <Setter Target="ContentPresenter.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundSelected}" />
                                                        <Setter Target="SelectionIndicator.Fill"
                                                                Value="{ThemeResource TreeViewItemSelectionIndicatorForeground}" />
                                                        <Setter Target="CollapsedGlyph.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundSelected}" />
                                                        <Setter Target="ExpandedGlyph.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundSelected}" />
                                                        <Setter Target="ContentPresenterGrid.BorderBrush"
                                                                Value="{ThemeResource TreeViewItemBorderBrushSelected}" />
                                                        <Setter Target="SelectionIndicator.Opacity" Value="1" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="Disabled">
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenterGrid.Background"
                                                                Value="{ThemeResource TreeViewItemBackgroundDisabled}" />
                                                        <Setter Target="ContentPresenter.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundDisabled}" />
                                                        <Setter Target="SelectionIndicator.Fill"
                                                                Value="{ThemeResource TreeViewItemSelectionIndicatorForegroundDisabled}" />
                                                        <Setter Target="CollapsedGlyph.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundDisabled}" />
                                                        <Setter Target="ExpandedGlyph.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundDisabled}" />
                                                        <Setter Target="ContentPresenterGrid.BorderBrush"
                                                                Value="{ThemeResource TreeViewItemBorderBrushDisabled}" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="PointerOverSelected">
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenterGrid.Background"
                                                                Value="{ThemeResource TreeViewItemBackgroundSelectedPointerOver}" />
                                                        <Setter Target="ContentPresenter.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundSelectedPointerOver}" />
                                                        <Setter Target="SelectionIndicator.Fill"
                                                                Value="{ThemeResource TreeViewItemSelectionIndicatorForegroundPointerOver}" />
                                                        <Setter Target="CollapsedGlyph.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundSelectedPointerOver}" />
                                                        <Setter Target="ExpandedGlyph.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundSelectedPointerOver}" />
                                                        <Setter Target="ContentPresenterGrid.BorderBrush"
                                                                Value="{ThemeResource TreeViewItemBorderBrushSelectedPointerOver}" />
                                                        <Setter Target="SelectionIndicator.Opacity" Value="1" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="PressedSelected">
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenterGrid.Background"
                                                                Value="{ThemeResource TreeViewItemBackgroundSelectedPressed}" />
                                                        <Setter Target="ContentPresenter.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundSelectedPressed}" />
                                                        <Setter Target="SelectionIndicator.Fill"
                                                                Value="{ThemeResource TreeViewItemSelectionIndicatorForegroundPressed}" />
                                                        <Setter Target="CollapsedGlyph.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundSelectedPressed}" />
                                                        <Setter Target="ExpandedGlyph.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundSelectedPressed}" />
                                                        <Setter Target="ContentPresenterGrid.BorderBrush"
                                                                Value="{ThemeResource TreeViewItemBorderBrushSelectedPressed}" />
                                                        <Setter Target="SelectionIndicator.Opacity" Value="1" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="SelectedDisabled">
                                                    <VisualState.Setters>
                                                        <Setter Target="ContentPresenterGrid.Background"
                                                                Value="{ThemeResource TreeViewItemBackgroundSelectedDisabled}" />
                                                        <Setter Target="ContentPresenter.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundSelectedDisabled}" />
                                                        <Setter Target="SelectionIndicator.Fill"
                                                                Value="{ThemeResource TreeViewItemSelectionIndicatorForegroundDisabled}" />
                                                        <Setter Target="CollapsedGlyph.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundSelectedDisabled}" />
                                                        <Setter Target="ExpandedGlyph.Foreground"
                                                                Value="{ThemeResource TreeViewItemForegroundSelectedDisabled}" />
                                                        <Setter Target="ContentPresenterGrid.BorderBrush"
                                                                Value="{ThemeResource TreeViewItemBorderBrushSelectedDisabled}" />
                                                        <Setter Target="SelectionIndicator.Opacity" Value="1" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="ReorderedPlaceholder">
                                                    <Storyboard>
                                                        <FadeOutThemeAnimation TargetName="MultiSelectGrid" />
                                                    </Storyboard>
                                                </VisualState>
                                            </VisualStateGroup>
                                            <VisualStateGroup x:Name="TreeViewMultiSelectStates">
                                                <VisualState x:Name="TreeViewMultiSelectDisabled" />
                                                <VisualState x:Name="TreeViewMultiSelectEnabledUnselected">
                                                    <VisualState.Setters>
                                                        <Setter Target="MultiSelectCheckBox.Visibility" Value="Visible" />
                                                        <Setter Target="ExpandCollapseChevron.Padding" Value="0,0,14,0" />
                                                        <Setter Target="ContentPresenterGrid.Padding" Value="0" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                                <VisualState x:Name="TreeViewMultiSelectEnabledSelected">
                                                    <VisualState.Setters>
                                                        <Setter Target="MultiSelectCheckBox.Visibility" Value="Visible" />
                                                        <Setter Target="MultiSelectGrid.Background"
                                                                Value="{ThemeResource TreeViewItemBackgroundSelected}" />
                                                        <Setter Target="MultiSelectGrid.BorderBrush"
                                                                Value="{ThemeResource TreeViewItemMultiSelectBorderBrushSelected}" />
                                                        <Setter Target="ExpandCollapseChevron.Padding" Value="0,0,14,0" />
                                                        <Setter Target="ContentPresenterGrid.Padding" Value="0" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                            </VisualStateGroup>
                                            <VisualStateGroup x:Name="DragStates">
                                                <VisualState x:Name="NotDragging" />
                                                <VisualState x:Name="MultipleDraggingPrimary">
                                                    <VisualState.Setters>
                                                        <Setter Target="MultiSelectCheckBox.Opacity" Value="0" />
                                                        <Setter Target="MultiArrangeOverlayTextBorder.Visibility"
                                                                Value="Visible" />
                                                    </VisualState.Setters>
                                                </VisualState>
                                            </VisualStateGroup>

                                        </VisualStateManager.VisualStateGroups>
                                        <Rectangle x:Name="SelectionIndicator" Width="3" Height="16"
                                                   Fill="{ThemeResource TreeViewItemSelectionIndicatorForeground}"
                                                   Opacity="0" HorizontalAlignment="Left" VerticalAlignment="Center"
                                                   RadiusX="2" RadiusY="2" />
                                        <Grid x:Name="MultiSelectGrid"
                                              Margin="{ThemeResource TreeViewItemMultiSelectSelectedItemBorderMargin}"
                                              Padding="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TreeViewItemTemplateSettings.Indentation}"
                                              BorderBrush="Transparent"
                                              BorderThickness="{ThemeResource TreeViewItemBorderThemeThickness}"
                                              CornerRadius="{ThemeResource ControlCornerRadius}">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition />
                                            </Grid.ColumnDefinitions>
                                            <Grid Grid.Column="0">
                                                <CheckBox x:Name="MultiSelectCheckBox" Width="32" MinWidth="32"
                                                          MinHeight="{ThemeResource TreeViewItemMultiSelectCheckBoxMinHeight}"
                                                          Margin="10,0,0,0" VerticalAlignment="Center"
                                                          Visibility="Collapsed" IsTabStop="False"
                                                          AutomationProperties.AccessibilityView="Raw" />
                                                <Border x:Name="MultiArrangeOverlayTextBorder" Visibility="Collapsed"
                                                        IsHitTestVisible="False" MinWidth="20" Height="20"
                                                        VerticalAlignment="Center" HorizontalAlignment="Center"
                                                        Background="{ThemeResource SystemControlBackgroundAccentBrush}"
                                                        BorderThickness="1"
                                                        BorderBrush="{ThemeResource SystemControlBackgroundChromeWhiteBrush}"
                                                        CornerRadius="{ThemeResource ControlCornerRadius}">
                                                    <TextBlock x:Name="MultiArrangeOverlayText"
                                                               Text="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TreeViewItemTemplateSettings.DragItemsCount}"
                                                               Style="{ThemeResource CaptionTextBlockStyle}"
                                                               Foreground="{ThemeResource SystemControlForegroundChromeWhiteBrush}"
                                                               IsHitTestVisible="False" VerticalAlignment="Center"
                                                               HorizontalAlignment="Center"
                                                               AutomationProperties.AccessibilityView="Raw" />
                                                </Border>
                                            </Grid>
                                            <Grid x:Name="ExpandCollapseChevron" Grid.Column="1" Padding="14,0"
                                                  Width="Auto" Opacity="1"
                                                  Background="Transparent">
                                                <TextBlock x:Name="CollapsedGlyph"
                                                           Foreground="{TemplateBinding GlyphBrush}" Width="12"
                                                           Height="12"
                                                           Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TreeViewItemTemplateSettings.CollapsedGlyphVisibility}"
                                                           FontSize="{TemplateBinding GlyphSize}"
                                                           Text="{TemplateBinding CollapsedGlyph}"
                                                           FontFamily="{StaticResource SymbolThemeFontFamily}"
                                                           Padding="2" VerticalAlignment="Center"
                                                           AutomationProperties.AccessibilityView="Raw"
                                                           IsTextScaleFactorEnabled="False" IsHitTestVisible="False" />
                                                <TextBlock x:Name="ExpandedGlyph"
                                                           Foreground="{TemplateBinding GlyphBrush}" Width="12"
                                                           Height="12"
                                                           Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TreeViewItemTemplateSettings.ExpandedGlyphVisibility}"
                                                           FontSize="{TemplateBinding GlyphSize}"
                                                           Text="{TemplateBinding ExpandedGlyph}"
                                                           FontFamily="{StaticResource SymbolThemeFontFamily}"
                                                           Padding="2" VerticalAlignment="Center"
                                                           AutomationProperties.AccessibilityView="Raw"
                                                           IsTextScaleFactorEnabled="False" IsHitTestVisible="False" />
                                                <TextBlock x:Name="DefaultNodeGlyph"
                                                           Width="12"
                                                           Foreground="{ThemeResource SeparatorBrush}"
                                                           Height="12"
                                                           Visibility="{Binding HasChildren, Converter={StaticResource TreeNodeGlyphConverter}, ConverterParameter='default'}"
                                                           FontSize="7"
                                                           Text="&#xe91F;"
                                                           FontFamily="{StaticResource SymbolThemeFontFamily}"
                                                           Padding="2" VerticalAlignment="Center"
                                                           AutomationProperties.AccessibilityView="Raw"
                                                           IsTextScaleFactorEnabled="False" IsHitTestVisible="False" />
                                            </Grid>
                                            <ContentPresenter x:Name="ContentPresenter" Grid.Column="2"
                                                              ContentTransitions="{TemplateBinding ContentTransitions}"
                                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                                              Content="{TemplateBinding Content}"
                                                              Foreground="{TemplateBinding Foreground}"
                                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                              MinHeight="{ThemeResource TreeViewItemContentHeight}"
                                                              Margin="{TemplateBinding Padding}" />
                                        </Grid>
                                    </Grid>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </TreeView.ItemContainerStyle>
                <TreeView.ItemTemplate>
                    <DataTemplate                                           >
                        <TreeViewItem x:ConnectionId='42' IsExpanded="False"
                                                                                  
                                                                                      >
                            <TreeViewItem.ContextFlyout>
                                <MenuFlyout>
                                    <MenuFlyoutItem x:ConnectionId='43' Text="添加新任务" Icon="Add"                              
                                                                                                                                                                         />
                                    <MenuFlyoutItem x:ConnectionId='44' Text="添加子分类" Icon="Add"                                     
                                                                                                                                                                                />
                                    <MenuFlyoutItem x:ConnectionId='45' Text="编辑标题" Icon="Edit"                                
                                                                                                                                                                      />
                                    <MenuFlyoutItem x:ConnectionId='46' Text="修改截至时间" Icon="Calendar"                                     
                                                                                                                                                                      />
                                    <MenuFlyoutItem x:ConnectionId='47' Text="完成该分类" Icon="Accept"
                                                                                              
                                                                                                                                                                          />
                                    <MenuFlyoutItem x:ConnectionId='48' Text="清空回收站" Icon="Delete"
                                                                                         
                                                                                                                                                                              />
                                   
                                </MenuFlyout>
                            </TreeViewItem.ContextFlyout>
                            <StackPanel MinHeight="25">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <!-- <ColumnDefinition Width="Auto"/> -->
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                        <!-- <ColumnDefinition Width="Auto"/> -->
                                    </Grid.ColumnDefinitions>
                                    <!-- <FontIcon Grid.Column="0" Glyph="{x:Bind IconName, Mode=OneWay}" FontSize="16" Margin="0,0,8,0"/> -->
                                    <TextBlock x:ConnectionId='50' Grid.Column="0"                                   TextWrapping="Wrap"
                                               Style="{StaticResource BodyTextStyle}" />
                                    <!-- <TextBlock Grid.Column="1" -->
                                    <!--           Text="{x:Bind SubCategoryCount, Mode=OneWay}" -->
                                    <!--           Style="{StaticResource CaptionTextStyle}" -->
                                    <!--           Margin="12,0,0,0" Padding="0,0,5,0"/> -->
                                    <TextBlock x:ConnectionId='51' Grid.Column="1"
                                                                                     
                                               Style="{StaticResource CaptionTextStyle}"
                                               Margin="12,0,0,0" Padding="0,0,5,0" />
                                </Grid>
                                <TextBlock x:ConnectionId='49'
                                                                                                                      
                                    Style="{StaticResource CaptionTextStyle}"
                                                                                                                                     
                                                                                                                                
                                    FontSize="11"
                                    Margin="0,2,0,4" />
                            </StackPanel>
                        </TreeViewItem>
                    </DataTemplate>
                </TreeView.ItemTemplate>
            </TreeView>
        </ScrollViewer>

        <StackPanel Grid.Row="3" Margin="0,10,0,0">
            <FontIcon x:ConnectionId='10' Glyph="&#xE70D;" FontSize="8" Name="BottomHint" Visibility="Collapsed" />
        </StackPanel>

        <!-- Add List Button -->
        <Grid Grid.Row="4" Margin="0,16,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <Button x:ConnectionId='8' Content="+ 列表"
                    Style="{StaticResource SecondaryButtonStyle}"
                    HorizontalAlignment="Center"
                                                />

            <Button x:ConnectionId='9' Grid.Column="1"
                    x:Name="ManageCompletedListsButton"
                    ToolTipService.ToolTip="管理已完成的任务列表"
                                                            
                    Style="{StaticResource SecondaryButtonStyle}"
                    Margin="8,0,0,0">
                <FontIcon Glyph="&#xE930;" FontSize="16" />
            </Button>
        </Grid>

        <!-- Add List Dialog -->
        <ContentDialog x:ConnectionId='4' x:Name="AddListDialog"
                       Title="添加新列表"
                       PrimaryButtonText="确定"
                       SecondaryButtonText="取消"
                       DefaultButton="Primary"
                                                                                        
                                                                            
                                                                        >
            <StackPanel Spacing="16">
                <TextBox x:ConnectionId='5' x:Name="ListNameTextBox"
                         Header="列表名称"
                         PlaceholderText="请输入列表名称"
                                                                   />

                <ComboBox x:ConnectionId='6' x:Name="ListPriorityComboBox"
                          Header="优先级"
                          PlaceholderText="选择优先级"
                          SelectedIndex="1">
                    <ComboBoxItem Content="低" />
                    <ComboBoxItem Content="中" />
                    <ComboBoxItem Content="高" />
                </ComboBox>

                <CalendarDatePicker x:ConnectionId='7' x:Name="ListDueDatePicker"
                                    Header="到期时间"
                                    PlaceholderText="选择到期时间" />


            </StackPanel>
        </ContentDialog>
    </Grid>
</UserControl>

