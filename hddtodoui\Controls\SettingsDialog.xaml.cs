using System;
using HddtodoUI.Utilities;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;

namespace HddtodoUI.Controls
{
    public sealed partial class SettingsDialog : UserControl
    {
        public event EventHandler SettingsSaved;
        public event EventHandler DialogCancelled;

        public SettingsDialog()
        {
            this.InitializeComponent();
            LoadSettings();
        }

        private void LoadSettings()
        {
            // 加载当前设置
            AutoHideMainFormCheckBox.IsChecked = ConfigSettingsUtils.getAutoHideMainFormConfig();
            TaskPauseAutoSwitchCheckBox.IsChecked = ConfigSettingsUtils.getTaskPauseAutoSwitchToTaskSelectionConfig();
            CountTimeStrategyCheckBox.IsChecked = ConfigSettingsUtils.getCountTimeStrategyConfig();
            TaskStartAutoShowTaskDetailCheckBox.IsChecked = ConfigSettingsUtils.getTaskStartAutoShowTaskDetailConfig();
            TaskCompletedAutoSwitchCheckBox.IsChecked = ConfigSettingsUtils.GetTaskCompletedAutoSwitchToTaskSelectionConfig();
            TaskStepWindowEnabledCheckBox.IsChecked = ConfigSettingsUtils.GetTaskStepWindowEnabledConfig();
            RememberTaskDetailsWindowPositionCheckBox.IsChecked = ConfigSettingsUtils.GetRememberTaskDetailsWindowPositionConfig();
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            // 保存设置
            ConfigSettingsUtils.setAutoHideMainFormConfig(AutoHideMainFormCheckBox.IsChecked ?? false);
            ConfigSettingsUtils.setTaskPauseAutoSwitchToTaskSelectionConfig(TaskPauseAutoSwitchCheckBox.IsChecked ?? false);
            ConfigSettingsUtils.setCountTimeStrategyConfig(CountTimeStrategyCheckBox.IsChecked ?? false);
            ConfigSettingsUtils.setTaskStartAutoShowTaskDetailConfig(TaskStartAutoShowTaskDetailCheckBox.IsChecked ?? false);
            ConfigSettingsUtils.SetTaskCompletedAutoSwitchToTaskSelectionConfig(TaskCompletedAutoSwitchCheckBox.IsChecked ?? false);
            ConfigSettingsUtils.SetTaskStepWindowEnabledConfig(TaskStepWindowEnabledCheckBox.IsChecked ?? false);
            ConfigSettingsUtils.SetRememberTaskDetailsWindowPositionConfig(RememberTaskDetailsWindowPositionCheckBox.IsChecked ?? false);

            // 触发保存事件
            SettingsSaved?.Invoke(this, EventArgs.Empty);
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            // 触发取消事件
            DialogCancelled?.Invoke(this, EventArgs.Empty);
        }
    }
}
