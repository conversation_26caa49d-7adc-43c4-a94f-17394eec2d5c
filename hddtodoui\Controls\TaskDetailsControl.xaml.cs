using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using Windows.System;
using Windows.UI.Core;
using CommunityToolkit.WinUI;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.BackendStore;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Converters;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.UICoordinator;
using HddtodoUI.Utilities;
using HddtodoUI.Windows;
using Microsoft.UI;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Input;
using Microsoft.UI.Xaml.Media;
using Microsoft.UI.Xaml.Controls.Primitives;
using Microsoft.UI.Xaml.Documents;
using HorizontalAlignment = Microsoft.UI.Xaml.HorizontalAlignment;
using RoutedEventArgs = Microsoft.UI.Xaml.RoutedEventArgs;
using TaskStatus = HddtodoUI.Models.TaskStatus;
using Thickness = Microsoft.UI.Xaml.Thickness;
using Visibility = Microsoft.UI.Xaml.Visibility;

namespace HddtodoUI.Controls
{
    public class TaskPath
    {
        public string Name { get; set; }
        public long TaskId { get; set; }
    }
    
    public sealed partial class TaskDetailsControl : UserControl
    {
        private TomatoTaskManager _currentTaskTomatoManager;
        private TodoTaskViewObject _currentTaskViewObject;
        private bool _isNewTask;
        private bool _isTaskModified = false;
        private TaskReminder _currentTaskReminder;
        private TaskRestart _currentTaskRestart;
        private ITaskReminderStore _reminderStore;
        private ITaskRestartStore _restartStore;
        
        public TaskDetailsControl()
        {
            this.InitializeComponent();

            // Initialize timer


            // 初始化子任务控件
            TaskStepsControl.SetEnabled(true);

            // Load categories (this would typically come from a service)
            LoadCategories();

            
            // 初始状态下隐藏保存按钮
            SaveButton.Visibility = Visibility.Collapsed;
            BottomBarGrid.Height = 0; // 初始化时设置高度为0

            _reminderStore = StoreFactoryHolder.getTaskReminderStore();
            _restartStore = StoreFactoryHolder.getTaskRestartStore();
            
            var accelerator = new KeyboardAccelerator
            {
                Key = VirtualKey.S,
                Modifiers = VirtualKeyModifiers.Control 
            };
            accelerator.Invoked += (sender, args) =>
            {
                SaveTask();
                args.Handled = true;
            };
            
            this.KeyboardAccelerators.Add(accelerator);

            // 子任务控件已重构为独立控件
            
        }


        public bool IsTheSameTask(long taskId)
        {
            return _currentTaskTomatoManager.getTaskID() == taskId;
        }

        // Subtasks集合已移至SubtasksControl中

        public DateTime CurrentDateTime { get; } = DateTime.Now;


        public TomatoTaskManager getMyTaskTomatoManager() => _currentTaskTomatoManager;

      
        public event EventHandler BackRequested;

        public void LoadCategories()
        {
            // Placeholder - in a real app, these would come from a service or database
            CategoryComboBox.Items.Clear();
            
            TaskCategoryComboBoxHelper.MakeComboBoxItem(CategoryComboBox);
            CategoryComboBox.SelectedIndex = 0;
        }

        private void RefreshCheckBox()
        {
            TaskCompletionCheckBox.Checked -= TaskCompletionCheckBox_Checked;
            TaskCompletionCheckBox.Unchecked -= TaskCompletionCheckBox_Unchecked;

            TaskCompletionCheckBox.IsChecked = _currentTaskTomatoManager.IsCompleted;
            
            TaskCompletionCheckBox.Checked += TaskCompletionCheckBox_Checked;
            TaskCompletionCheckBox.Unchecked += TaskCompletionCheckBox_Unchecked;
        }
        
        private ObservableCollection<TaskPath> ParentTaskPaths = new ObservableCollection<TaskPath>{
           
        };
       
        public void LoadTask(TodoTaskViewObject taskViewObject)
        {
            _currentTaskViewObject = taskViewObject ?? new TodoTaskViewObject();
            _isNewTask = taskViewObject == null;

            _currentTaskTomatoManager = TaskTomatoManagerFactory.GetTomatoTaskManager(_currentTaskViewObject.TaskID);

            TaskTitleTextBox.TextChanged -= TaskProperty_Changed;
            DueDatePicker.DateChanged -= TaskProperty_Changed;
            PriorityComboBox.SelectionChanged -= TaskProperty_Changed;
            CategoryComboBox.SelectionChanged -= TaskProperty_Changed;
            NotesTextBox.TextChanged -= TaskProperty_Changed;

            // 刷新分类列表以确保显示最新数据
            LoadCategories();

            // Populate UI
            TaskTitleTextBox.Text = _currentTaskViewObject.Title;

            
            RefreshCheckBox();
     

            if (_currentTaskViewObject.DueDate.HasValue)
            {
                DueDatePicker.Date = _currentTaskViewObject.DueDate.Value;
            }
            else
            {
                DueDatePicker.Date = null;
            }

            // Set priority
            switch (_currentTaskViewObject.Priority)
            {
                case TaskPriority.low:
                    PriorityComboBox.SelectedIndex = 0;
                    break;
                case TaskPriority.normal:
                    PriorityComboBox.SelectedIndex = 1;
                    break;
                case TaskPriority.high:
                    PriorityComboBox.SelectedIndex = 2;
                    break;
            }

            // Set category
            for (int i = 0; i < CategoryComboBox.Items.Count; i++)
            {
                var item = CategoryComboBox.Items[i] as ComboBoxItem;
                if (item != null && item.Tag.ToString() == _currentTaskViewObject.Category)
                {
                    CategoryComboBox.SelectedIndex = i;
                    break;
                }
            }

            // Set notes
            NotesTextBox.Text = _currentTaskViewObject.Notes;
            NotesTextBox.Focus(FocusState.Keyboard);
            // 设置任务步骤控件的父任务
            TaskStepsControl.SetParentTask(_currentTaskViewObject);


            // Update UI state
            UpdateTimerControls();
            CreatedDateTextBlock.Text = _currentTaskViewObject.StartTime.ToString();

            TaskTitleTextBox.TextChanged += TaskProperty_Changed;
            DueDatePicker.DateChanged += TaskProperty_Changed;
            PriorityComboBox.SelectionChanged += TaskProperty_Changed;
            CategoryComboBox.SelectionChanged += TaskProperty_Changed;
            NotesTextBox.TextChanged += TaskProperty_Changed;


            // 重置修改状态
            _isTaskModified = false;
            LogService.Instance.Info("_isTaskModified = false;");
            SaveButton.Visibility = Visibility.Collapsed;
            BottomBarGrid.Height = 0; // 隐藏时设置高度为0

            //TaskTitleTextBox.Focus(FocusState.Unfocused);
            //RefreshSubtasks();
            
            LoadTaskReminder();
            LoadTaskRestart();
            
            ParentTaskPaths.Clear();
            taskViewObject.GetParentTaskIds().ForEach(t => ParentTaskPaths.Add(
                new TaskPath { Name = StoreFactoryHolder.getTaskStore().getTaskById(t,UserInfoHolder.getUserId()).Title, TaskId = t }));;
            BreadcrumbBar1.ItemsSource = ParentTaskPaths;
            
            if (ParentTaskPaths.Count >0)
                IndicatorIcon.Visibility = Visibility.Visible;
            else
                IndicatorIcon.Visibility = Visibility.Collapsed;
        }
        
        public bool GetIsModified() => _isTaskModified;
        
      
        
        public void AskSaveOrDismissBeforeClose()
        {
            if (GetIsModified() == false) return;
            var taskTitle = _currentTaskViewObject.Title;
            var result = MessageBox.Show($"要保存 任务 {taskTitle} 修改吗？选否将会丢弃修改", "确认保存", MessageBoxButton.YesNo);
            if (result == MessageBoxResult.Yes)
            {
               SaveTask();
            }
            else
            {
                _isTaskModified = false;
            }
        }
        
        

        public async void CheckModifiedAndLoadTask(TodoTaskViewObject taskViewObject)
        {
            AskSaveOrDismissBeforeClose();
            // 检查是否有修改
            // if (GetIsModified())
            // {
            //     // 如果有修改，询问用户是否保存
            //     var msgDialog = new ContentDialog
            //     {
            //         Title = "有未保存的修改",
            //         Content = "是否保存当前任务的修改？",
            //         PrimaryButtonText = "保存",
            //         SecondaryButtonText = "不保存",
            //         DefaultButton = ContentDialogButton.Primary,
            //         XamlRoot = this.XamlRoot
            //     };
            //     
            //     var result = await msgDialog.ShowAsync();
            //
            //     if (result == ContentDialogResult.Primary)
            //     {
            //         // 用户选择保存，调用SaveTask()保存修改
            //         SaveTask();
            //     }
            //     
            //     //如果是同一个任务不用加载
            //     if ( _currentTaskViewObject != null)
            //         if ( _currentTaskViewObject.TaskID == taskViewObject.TaskID)
            //         {
            //             // 即使是同一个任务，也要刷新分类列表，因为任务可能被拖拽到了新分类
            //             LoadCategories();
            //             // 重新设置分类选择
            //             for (int i = 0; i < CategoryComboBox.Items.Count; i++)
            //             {
            //                 var item = CategoryComboBox.Items[i] as ComboBoxItem;
            //                 if (item != null && item.Tag.ToString() == taskViewObject.Category)
            //                 {
            //                     CategoryComboBox.SelectedIndex = i;
            //                     break;
            //                 }
            //             }
            //             return;
            //         }
            //     
            //     // 无论用户是否选择保存，都继续加载新任务
            // }
          
            
            // 加载新任务
            LoadTask(taskViewObject);
        }

        public void UpdateTimerControls()
        {
            if (_currentTaskViewObject == null) return;

            TimerTextBlock.Text = _currentTaskTomatoManager.GetRemainTimeString();
            
            RefreshCheckBox();

            if (_currentTaskTomatoManager.IsCompleted)
            {
                TaskTitleTextBox.Foreground = new SolidColorBrush(Colors.Gray);
                TimerTextBlock.Foreground = new SolidColorBrush(Colors.Gray);
                StartButton.IsEnabled = false;
                //SubtasksControl.SetEnabled(false);
            }
            else
            {
                TaskTitleTextBox.Foreground = new SolidColorBrush(Colors.Black);
                TimerTextBlock.Foreground = new SolidColorBrush(Colors.Black);
                StartButton.IsEnabled = true;
                //SubtasksControl.SetEnabled(true);
            }


            SetStartButtonIcon(TaskStatusExtensions.GetTaskStatus(_currentTaskTomatoManager.getTaskStatus()));
        }

        public void OnTaskTickStatusChanged(TomatoTaskManager ttm)
        {
            if (ttm.getTaskID() == _currentTaskViewObject.TaskID)
                UpdateTimerControls();
        }

        public void SetStartButtonIcon(TaskStatus uiStatus)
        {
            var fontIcon = StartButton.Content as FontIcon;
            if (fontIcon != null)
            {
                // 手动更新FontIcon
                string glyph =
                    (new TaskStatusIconConverter()).Convert(uiStatus, null, null, null).ToString();
                if (!string.IsNullOrEmpty(glyph))
                {
                    fontIcon.Glyph = glyph;
                }

                var color =
                    (new TaskStatusColorConverter()).Convert(uiStatus, null, null, null) as Brush;
                if (color != null)
                {
                    fontIcon.Foreground = color;
                }
            }
        }

        private void StartButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentTaskTomatoManager != null)
                TaskUICoordinatorFactory.Instance(_currentTaskTomatoManager.getTask()).StartOrPauseButtonClick(_currentTaskTomatoManager);
        }


        private async void BackButton_Click(object sender, RoutedEventArgs e)
        {
            AskSaveOrDismissBeforeClose();
            BackRequested?.Invoke(this, EventArgs.Empty);
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            SaveTask();
        }

       

        private void SaveTask()
        {
            if (_currentTaskViewObject == null) return;

            var modifiedTaskViewObject = new TodoTaskViewObject();
            // Update task from UI
            modifiedTaskViewObject.Title = TaskTitleTextBox.Text;
            modifiedTaskViewObject.DueDate = DueDatePicker.Date?.Date.AddMinutes(5);

            // Get priority
            if (PriorityComboBox.SelectedIndex >= 0)
            {
                var selectedItem = PriorityComboBox.SelectedItem as ComboBoxItem;
                if (selectedItem != null && selectedItem.Tag != null)
                {
                    modifiedTaskViewObject.Priority =
                        (TaskPriority)Enum.Parse(typeof(TaskPriority), selectedItem.Tag.ToString());
                }
            }

            // Get category
            if (CategoryComboBox.SelectedIndex >= 0)
            {
                var selectedItem = CategoryComboBox.SelectedItem as ComboBoxItem;
                if (selectedItem != null && selectedItem.Tag != null)
                {
                    modifiedTaskViewObject.Category = selectedItem.Tag.ToString();
                }
            }

            // Set notes
            modifiedTaskViewObject.Notes = NotesTextBox.Text;
            
            // Save subtasks (placeholder - in a real app, this would update a service)
            // _currentTask.Subtasks = new List<TodoTask>(Subtasks);

            TTask task = _currentTaskTomatoManager.getTask();

            task.Title = modifiedTaskViewObject.Title;
            task.TaskDueTime = modifiedTaskViewObject.DueDate;
            task.Priority = modifiedTaskViewObject.Priority;
            task.BelongToListKey = modifiedTaskViewObject.Category;
            task.TaskRemark = modifiedTaskViewObject.Notes;

            StoreFactoryHolder.getTaskStore().saveTaskChange(task, UserInfoHolder.getUserId());

            TaskUICoordinatorFactory.Instance(task).OnTaskModified(task, _currentTaskViewObject);

            TaskCategory taskList = StoreFactoryHolder.getTaskCategoryStore()
                .GetTaskCategoryByKey(task.BelongToListKey, UserInfoHolder.getUserId());;
            
            _currentTaskViewObject = TodoTaskViewObject.GetFrom(task,taskList);

            // 重置修改状态
            _isTaskModified = false;
            LogService.Instance.Info("_isTaskModified = false;");
            SaveButton.Visibility = Visibility.Collapsed;
            BottomBarGrid.Height = 0; // 隐藏时设置高度为0
            
            NotificationService.Instance.ShowNotification(task.Title,NotificationLevel.Success,"任务保存成功");

          
        }

        private void TaskProperty_Changed(object sender, object e)
        {
            if (_currentTaskViewObject != null)
            {
                LogService.Instance.Info("_isTaskModified = true;");
                SwitchToWantToSaveUI();
            }
        }
        
        private void SwitchToWantToSaveUI()
        {
            _isTaskModified = true;
            SaveButton.Visibility = Visibility.Visible;
            BottomBarGrid.Height = 50; // 显示时设置固定高度
        }

        // 子任务相关方法已移至 SubtasksControl 中
        
        // 注意：这个方法在使用TaskStepsControl后可能需要重新考虑其实现
        // 因为TaskStepsControl不再使用TaskItemControl
        public TaskItemControl GetSubTaskItemControlByTaskId(long taskId)
        {
            // 这里需要根据实际情况调整
            // 如果任务步骤不再使用TaskItemControl，可能需要返回null或者实现新的接口
            return null;
        }

        private void RefreshTaskSteps()
        {
            // 使用TaskStepsControl刷新任务步骤列表
            if (_currentTaskViewObject != null)
            {
                TaskStepsControl.SetParentTask(_currentTaskViewObject);
            }
        }

        private void TaskCompletionCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            if ( _currentTaskTomatoManager != null)
                TaskUICoordinatorFactory.Instance(_currentTaskTomatoManager.getTask()).TaskSwitchComplete(_currentTaskTomatoManager.getTask());
        }

        private void TaskCompletionCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            if ( _currentTaskTomatoManager != null)
                TaskUICoordinatorFactory.Instance(_currentTaskTomatoManager.getTask()).TaskSwitchUnComplete(_currentTaskTomatoManager.getTask());
        }

       
        // 时钟事件
        public void TomatoTicked()
        {
            
            if (_currentTaskViewObject == null) 
                return;
            
            if ( CurrentStatusHolder.getCurrentStatusHolder().IsCurrentRunningTask(_currentTaskViewObject.TaskID))
            {
                UpdateTimerControls();
            }
        
        }

        private async void SetReminderButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentTaskViewObject == null)
                return;

            try
            {
                var dialog = _currentTaskReminder == null ? 
                    new TaskReminderDialog() : 
                    new TaskReminderDialog(_currentTaskReminder);
                
                // 设置XamlRoot，确保对话框能够正确显示
                dialog.XamlRoot = this.XamlRoot;
                
                // 显示对话框
                var result = await dialog.ShowAsync();
                
                if (result == ContentDialogResult.Primary) // 用户点击了保存按钮
                {
                    var reminder = dialog.Reminder;
                    reminder.TaskId = _currentTaskViewObject.TaskID;
                    reminder.UserId = UserInfoHolder.getUserId();
                    reminder.NextRemindTime = reminder.RemindTime;
                    
                    if (_currentTaskReminder == null) // 创建新提醒
                    {
                        _currentTaskReminder = _reminderStore.CreateReminder(UserInfoHolder.getUserId(), reminder);
                    }
                    else // 更新现有提醒
                    {
                        _reminderStore.UpdateReminder(UserInfoHolder.getUserId(), _currentTaskReminder.ReminderId, reminder);
                        _currentTaskReminder = reminder;
                    }
                    
                    // 更新UI显示
                    UpdateReminderInfoDisplay();
                }
                else if (result == ContentDialogResult.None) // 用户点击了删除按钮
                {
                    if (_currentTaskReminder != null)
                    {
                        // 删除提醒
                        _reminderStore.DeleteReminder(UserInfoHolder.getUserId(), _currentTaskReminder.ReminderId);
                        _currentTaskReminder = null;
                        
                        // 更新UI显示
                        UpdateReminderInfoDisplay();
                    }
                }
            }
            catch (Exception ex)
            {
                // 处理异常
                LogService.Instance.Debug($"设置提醒时出错: {ex.Message}");
            }
        }

        private async void LoadTaskReminder()
        {
            if (_currentTaskViewObject == null || UserInfoHolder.getUserInfo() == null)
            {
                _currentTaskReminder = null;
                return;
            }

            try
            {
                // 加载当前任务的提醒
                _currentTaskReminder =
                    await Task.Run(() => _reminderStore.FindReminderByTaskId(UserInfoHolder.getUserId(), _currentTaskViewObject.TaskID));
                  
                UpdateReminderInfoDisplay();
            }
            catch (Exception ex)
            {
                LogService.Instance.Debug($"加载任务提醒时出错: {ex.Message}");
                _currentTaskReminder = null;
            }
        }

        private void UpdateReminderInfoDisplay()
        {
            if (_currentTaskReminder == null)
            {
                // 没有提醒，显示默认文本
                ReminderInfoTextBlock.Text = "未设置提醒";
                return;
            }

            // 计算下次提醒时间
            DateTime? nextReminder = _currentTaskReminder.CaculateNextRemindTime();

            // 显示提醒信息
            ReminderInfoTextBlock.Text = _currentTaskReminder.GetNextRemindTimeDisplayInfo();
        }
        
        private async void SetRestartButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentTaskViewObject == null)
                return;

            try
            {
                var dialog = _currentTaskRestart == null ? 
                    new TaskRestartDialog() : 
                    new TaskRestartDialog(_currentTaskRestart);
                
                // 设置XamlRoot，确保对话框能够正确显示
                dialog.XamlRoot = this.XamlRoot;
                
                // 显示对话框
                var result = await dialog.ShowAsync();
                
                if (result == ContentDialogResult.Primary) // 用户点击了保存按钮
                {
                    var restart = dialog.Restart;
                    restart.TaskId = _currentTaskViewObject.TaskID;
                    restart.UserId = UserInfoHolder.getUserId();
                    
                    if (_currentTaskRestart == null) // 创建新重启设置
                    {
                        // 创建请求对象
                        var createRequest = new CreateTaskRestartRequest
                        {
                            TaskId = restart.TaskId,
                            Period = restart.Period,
                            FirstSettingRestartTime = restart.FirstSettingRestartTime,
                            TaskPeriodSpanCount = restart.TaskPeriodSpanCount
                        };
                        
                        _currentTaskRestart = _restartStore.CreateTaskRestart(UserInfoHolder.getUserId(), createRequest);
                    }
                    else // 更新现有重启设置
                    {
                        // 创建更新请求对象
                        var updateRequest = new UpdateTaskRestartRequest
                        {
                            Period = restart.Period,
                            FirstSettingRestartTime = restart.FirstSettingRestartTime,
                            TaskPeriodSpanCount = restart.TaskPeriodSpanCount
                        };
                        
                        _restartStore.UpdateTaskRestart(UserInfoHolder.getUserId(), _currentTaskRestart.RestartId, updateRequest);
                        _currentTaskRestart = restart;
                    }
                    
                    // 更新UI显示
                    UpdateRestartInfoDisplay();
                }
                else if (result == ContentDialogResult.None) // 用户点击了删除按钮
                {
                    if (_currentTaskRestart != null)
                    {
                        // 删除重启设置
                        _restartStore.DeleteTaskRestart(UserInfoHolder.getUserId(), _currentTaskRestart.RestartId);
                        _currentTaskRestart = null;
                        
                        // 更新UI显示
                        UpdateRestartInfoDisplay();
                    }
                }
            }
            catch (Exception ex)
            {
                // 处理异常
                LogService.Instance.Info($"设置任务重复时出错: {ex.Message}");
            }
        }

        private async void LoadTaskRestart()
        {
            if (_currentTaskViewObject == null || UserInfoHolder.getUserInfo() == null)
            {
                _currentTaskRestart = null;
                return;
            }

            try
            {
                // 加载当前任务的重启设置
                _currentTaskRestart = await Task.Run(()=>_restartStore.FindTaskRestartByTaskId(_currentTaskViewObject.TaskID, UserInfoHolder.getUserId()));
                UpdateRestartInfoDisplay();
            }
            catch (Exception ex)
            {
                LogService.Instance.Debug($"加载任务重复设置时出错: {ex.Message}");
                _currentTaskRestart = null;
            }
        }

        private void UpdateRestartInfoDisplay()
        {
            if (_currentTaskRestart == null)
            {
                // 没有重启设置，显示默认文本
                RestartInfoTextBlock.Text = "未设置重复";
                return;
            }

            // 显示重启信息
            string periodInfo = GetPeriodDisplayInfo(_currentTaskRestart);
            RestartInfoTextBlock.Text = $"{_currentTaskRestart.TaskNextRestartDateTime.ToString("yyyy/MM/dd HH:mm")}, {periodInfo}";
        }
        
        private string GetPeriodDisplayInfo(TaskRestart restart)
        {
            if (restart.Period == TaskPeriod.none)
            {
                return "一次性重启";
            }

            string unit = restart.Period switch
            {
                TaskPeriod.hour => "小时",
                TaskPeriod.daily => "天",
                TaskPeriod.weekly => "周",
                TaskPeriod.monthly => "月",
                TaskPeriod.yearly => "年",
                _ => ""
            };

            return $"每 {restart.TaskPeriodSpanCount} {unit}";
        }


     

        private void BreadcrumbBarItem_Click(object sender, RoutedEventArgs e)
        {
            var toggleButton = sender as ToggleButton;

            if (toggleButton != null)
            {
                long taskId = toggleButton.Tag as long? ?? 0;
                if( taskId == 0) return;

                var task = StoreFactoryHolder.getTaskStore().getTaskById(taskId, UserInfoHolder.getUserId());
                var list = StoreFactoryHolder.getTaskCategoryStore().GetTaskCategoryByKey(task.BelongToListKey, UserInfoHolder.getUserId());
                TheUICoordinator.Instance.ShowTaskDetailsWindow(TodoTaskViewObject.GetFrom(task,list));
            }
        }

        private async void TimerTextBlock_DoubleTapped(object sender, DoubleTappedRoutedEventArgs e)
        {
            if (_currentTaskViewObject == null || _currentTaskTomatoManager == null)
                return;
                
            // 创建NumberBox用于输入新的TomatoTimeSpan值
            var inputBox = new TextBox
            {
                Text = _currentTaskViewObject.Task.TomatoTimeSpan.ToString(),
                PlaceholderText = "请输入番茄时间（秒）",
                HorizontalAlignment = HorizontalAlignment.Stretch,
                Margin = new Thickness(0, 10, 0, 0)
            };
            
            // 创建对话框
            var dialog = new ContentDialog
            {
                Title = "修改番茄时间",
                Content = inputBox,
                PrimaryButtonText = "确定",
                SecondaryButtonText = "取消",
                DefaultButton = ContentDialogButton.Primary,
                XamlRoot = this.XamlRoot
            };
            
            // 显示对话框并获取结果
            var result = await dialog.ShowAsync();
            
            // 如果用户点击了确定按钮
            if (result == ContentDialogResult.Primary)
            {
                // 尝试解析用户输入的值
                if (int.TryParse(inputBox.Text, out int newTimeSpan) && newTimeSpan > 0)
                {
                    // 更新TomatoTimeSpan值
                    var task = _currentTaskTomatoManager.getTask();
                    task.TomatoTimeSpan = newTimeSpan;
                    TimerTextBlock.Text = _currentTaskTomatoManager.GetRemainTimeString();
                    // 标记为已修改
                   SwitchToWantToSaveUI();
                  
                }
            }
        }
    }
}