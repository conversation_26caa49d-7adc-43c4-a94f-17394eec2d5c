using System;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.Models;
using HddtodoUI.TaskTomatoManager.Constants;
using HddtodoUI.Utilities;
using LanguageExt;
using static LanguageExt.Prelude; 

namespace HddtodoUI.TaskTomatoManager
{
    public class CurrentStatusHolder
    {
        private static CurrentStatusHolder _currentStatusHolder = null;

        private String _currentSpecialListName;

        private TaskCategory _currentTaskList;
        
        private TomatoTaskManager _currentTomatoTaskManager;

        public void setCurrentTaskList(TaskCategory myTaskList, string specialListName = null)
        {
            if (myTaskList == null && String.IsNullOrWhiteSpace(specialListName))
                throw new Exception("taskList can't be null");

            this._currentTaskList = myTaskList;
            this._currentSpecialListName = specialListName;
        }

        public void setCurrentTomatoTask(TomatoTaskManager myTomatoTaskManager)
        {
            this._currentTomatoTaskManager = myTomatoTaskManager;
        }
        
        public Option<TomatoTaskManager> getCurrentTomatoTaskOption()
        {
            return _currentTomatoTaskManager;
        }

        public bool isCurrentASpecialList()
        {
            return _currentTaskList == null && (!String.IsNullOrEmpty(_currentSpecialListName));
        }

        public String getCurrentSpecialListName()
        {
            return _currentSpecialListName;
        }

        public Tomato getCurrentTomato()
        {
            if (_currentTomatoTaskManager == null) return null;
            return _currentTomatoTaskManager.getTomatoMayBeNull();
        }

        public bool IsCurrentRunningTask(long taskId)
        {
            if (_currentTomatoTaskManager == null) return false;
            return _currentTomatoTaskManager.getTaskID() == taskId;
        }

        public TaskCategory getCurrentTaskList()
        {
            //这里要加上处理日期TaskList的能力
            if (this._currentTaskList == null)
                if (StoreFactoryHolder.getTaskListStore()
                    .hasTaskListByKey(SpecialTaskListConstants.CollectBox, UserInfoHolder.getUserId()))
                    return StoreFactoryHolder.getTaskCategoryStore().GetTaskCategoryByKey(SpecialTaskListConstants.CollectBox,
                        UserInfoHolder.getUserId());

            return this._currentTaskList;
        }

        public bool isTaskInCurrentDisplayList(TTask task)
        {
            return isTaskInCurrentDisplayList(task.BelongToListKey, task.TaskDueTime,task.Priority);
        }

        public bool IsCurrentListIsDeletedList()
        {
            if( CurrentStatusHolder.getCurrentStatusHolder().isCurrentASpecialList())
                if (CurrentStatusHolder.getCurrentStatusHolder().getCurrentSpecialListName() ==
                    SpecialTaskListConstants.Deleted)
                    return true;
            
            return false;
        }
        
        public bool isTaskInCurrentDisplayList(TodoTaskViewObject vo)
        {
            return isTaskInCurrentDisplayList(vo.Category, vo.DueDate,vo.Priority);
        }

        public bool isTaskInCurrentDisplayList(String listKey, DateTime? taskDueTime,TaskPriority priority)
        {
            if (!this.isCurrentASpecialList())
            {
                return getCurrentTaskList().Key == listKey;
            }
            else
            {
                if (getCurrentSpecialListName() == SpecialTaskListConstants.Today)
                {
                    return DateUtils.IsDateBelongToTodayDueTask(taskDueTime);
                }

                if (getCurrentSpecialListName() == SpecialTaskListConstants.Week)
                {
                    return DateUtils.IsDateBelongToWeekDueTask(taskDueTime);
                }

                if (getCurrentSpecialListName() == SpecialTaskListConstants.Important)
                {
                    return priority == TaskPriority.high;
                }
                
            }

            return false;
        }

      

        static public CurrentStatusHolder getCurrentStatusHolder()
        {
            if (_currentStatusHolder == null)
            {
                _currentStatusHolder = new CurrentStatusHolder();
            }


            return _currentStatusHolder;
        }
    }
}