<?xml version="1.0" encoding="utf-8"?>
<UserControl
    x:Class="HddtodoUI.Controls.EditableStatusDescriptionControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid Padding="10,0,30,0">
        <!-- 显示模式 -->
        <Grid x:Name="DisplayModeGrid" Visibility="Visible" >
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock x:Name="StatusTextBlock"
                       Grid.Column="0"
                       Text="{x:Bind StatusDescription, Mode=OneWay}"
                       Style="{StaticResource CaptionTextStyle}"
                       Foreground="{ThemeResource UserInfoTextBrush}"
                       VerticalAlignment="Center"
                       TextWrapping="Wrap"
                       Margin="0,4"
                       FontSize="15"
                       ToolTipService.ToolTip="双击可以编辑状态描述"
                       DoubleTapped="StatusTextBlock_DoubleTapped"/>
            
            <Button x:Name="EditButton" 
                    Grid.Column="1"
                    Content="&#xE70F;"
                    FontFamily="Segoe MDL2 Assets"
                    Style="{StaticResource TransparentButtonStyle}"
                    Foreground="{ThemeResource UserInfoTextBrush}"
                    VerticalAlignment="Center"
                    Margin="8,0,0,0"
                    Opacity="0"
                    Click="EditButton_Click"
                    ToolTipService.ToolTip="编辑状态描述"/>
        </Grid>
        
        <!-- 编辑模式 -->
        <Grid x:Name="EditModeGrid" Visibility="Collapsed">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBox x:Name="StatusTextBox"
                     Grid.Column="0"
                     Text="{x:Bind StatusDescription, Mode=TwoWay}"
                     PlaceholderText="输入当前状态描述..."
                     VerticalAlignment="Center"
                     Margin="0,4"
                     KeyDown="StatusTextBox_KeyDown"/>
            
            <Button x:Name="SaveButton" 
                    Grid.Column="1"
                    Content="&#xE74E;"
                    FontFamily="Segoe MDL2 Assets"
                    Style="{StaticResource TransparentButtonStyle}"
                    Foreground="{ThemeResource SystemColorHighlightColor}"
                    VerticalAlignment="Center"
                    Margin="8,0,4,0"
                    Click="SaveButton_Click"
                    ToolTipService.ToolTip="保存"/>
            
            <Button x:Name="CancelButton" 
                    Grid.Column="2"
                    Content="&#xE711;"
                    FontFamily="Segoe MDL2 Assets"
                    Style="{StaticResource TransparentButtonStyle}"
                    Foreground="{ThemeResource SystemColorGrayTextColor}"
                    VerticalAlignment="Center"
                    Margin="4,0,0,0"
                    Click="CancelButton_Click"
                    ToolTipService.ToolTip="取消"/>
        </Grid>
    </Grid>
</UserControl>
