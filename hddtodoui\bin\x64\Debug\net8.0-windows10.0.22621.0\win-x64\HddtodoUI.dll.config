<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5.2" />
    </startup>
    <appSettings>
        <add key="baseurl" value="http://localhost:9300/api" />
        <add key="toolTipShowGap" value="300" />
        <add key="username" value="hongdengdao" />
        <add key="password" value="123456" />
        <add key="IndicatorFormLeft" value="999" />
        <add key="IndicatorFormTop" value="1279" />
        <add key="IndicatorFormScreen" value="\\.\DISPLAY2" />
        <add key="IndicatorFormScreenBounds" value="0,0,2560,1440" />
        <add key="AutoHideMainForm" value="True" />
        <add key="ClientId" value="1be5488f-2709-477f-b240-f0aea9c32e69" />
        <add key="TaskPauseAutoSwitchToTaskSelection" value="False" />
        <add key="CountTimeStrategy" value="False" />
        <add key="TaskStartAutoShowTaskDetailConfig" value="False" />
        <add key="TaskFinishedAutoSwitchToTaskSelection" value="False" />
        <add key="EnableTaskStepWindow" value="True" />
        <add key="RememberTaskDetailsWindowPosition" value="True" />
        <add key="TaskDetailsWindowX" value="640" />
        <add key="TaskDetailsWindowY" value="196" />
        <add key="TaskDetailsWindowWidth" value="1280" />
        <add key="TaskDetailsWindowHeight" value="1000" />
        <add key="TaskDetailsWindowScreen" value="2560x1392" />
        <add key="UserStatusDescription" value="目标：初步 完成仓库管理" />
    </appSettings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Data.SQLite" publicKeyToken="db937bc2d44ff139" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*********" newVersion="*********" />
      </dependentAssembly>
        <dependentAssembly>
            <assemblyIdentity name="WinRT.Runtime" publicKeyToken="99ea127f02d97709" culture="neutral" />
            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
        </dependentAssembly>
    </assemblyBinding>
      
  </runtime>
</configuration>