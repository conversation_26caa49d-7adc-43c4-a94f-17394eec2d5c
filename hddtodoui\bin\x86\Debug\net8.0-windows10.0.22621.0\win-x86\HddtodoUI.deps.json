{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/win-x86", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/win-x86": {"HddtodoUI/1.0.0": {"dependencies": {"CommunityToolkit.WinUI.Controls.Primitives": "8.2.250402", "CommunityToolkit.WinUI.Controls.Sizers": "8.2.250402", "CommunityToolkit.WinUI.Extensions": "8.2.250402", "LanguageExt.Core": "4.4.9", "Microsoft.Windows.SDK.BuildTools": "10.0.26100.1742", "Microsoft.WindowsAppSDK": "1.7.250401001", "NLog": "5.4.0", "System.Configuration.ConfigurationManager": "10.0.0-preview.3.25171.5", "WindowsInput": "6.4.1", "Microsoft.Web.WebView2.Core.Projection": "1.0.2903.40", "runtimepack.Microsoft.NETCore.App.Runtime.win-x86": "8.0.8", "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x86": "8.0.8", "runtimepack.Microsoft.Windows.SDK.NET.Ref": "10.0.26100.57"}, "runtime": {"HddtodoUI.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x86/8.0.8": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.824.36612"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}}, "native": {"Microsoft.DiaSymReader.Native.x86.dll": {"fileVersion": "14.40.33810.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "8.0.824.36612"}, "clretwrc.dll": {"fileVersion": "8.0.824.36612"}, "clrgc.dll": {"fileVersion": "8.0.824.36612"}, "clrjit.dll": {"fileVersion": "8.0.824.36612"}, "coreclr.dll": {"fileVersion": "8.0.824.36612"}, "createdump.exe": {"fileVersion": "8.0.824.36612"}, "hostfxr.dll": {"fileVersion": "8.0.824.36612"}, "hostpolicy.dll": {"fileVersion": "8.0.824.36612"}, "mscordaccore.dll": {"fileVersion": "8.0.824.36612"}, "mscordaccore_x86_x86_8.0.824.36612.dll": {"fileVersion": "8.0.824.36612"}, "mscordbi.dll": {"fileVersion": "8.0.824.36612"}, "mscorrc.dll": {"fileVersion": "8.0.824.36612"}, "msquic.dll": {"fileVersion": "*******"}}}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x86/8.0.8": {"runtime": {"Accessibility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36606"}, "DirectWriteForwarder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "Microsoft.VisualBasic.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36606"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "********", "fileVersion": "8.0.824.36606"}, "Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "PresentationCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "PresentationFramework-SystemCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "PresentationFramework-SystemData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "PresentationFramework-SystemDrawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "PresentationFramework-SystemXml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "PresentationFramework-SystemXmlLinq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "PresentationFramework.Aero.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "PresentationFramework.Aero2.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "PresentationFramework.AeroLite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "PresentationFramework.Classic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "PresentationFramework.Luna.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "PresentationFramework.Royale.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "PresentationFramework.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "PresentationUI.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "ReachFramework.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36606"}, "System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36606"}, "System.Drawing.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36606"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36606"}, "System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Printing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Windows.Controls.Ribbon.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Windows.Forms.Design.Editors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36606"}, "System.Windows.Forms.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36606"}, "System.Windows.Forms.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36606"}, "System.Windows.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36606"}, "System.Windows.Input.Manipulations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "System.Windows.Presentation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "System.Xaml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "UIAutomationClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "UIAutomationClientSideProviders.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "UIAutomationProvider.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "UIAutomationTypes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}, "WindowsFormsIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36607"}}, "native": {"D3DCompiler_47_cor3.dll": {"fileVersion": "10.0.22621.3233"}, "PenImc_cor3.dll": {"fileVersion": "8.0.824.36607"}, "PresentationNative_cor3.dll": {"fileVersion": "*********504"}, "vcruntime140_cor3.dll": {"fileVersion": "14.40.33810.0"}, "wpfgfx_cor3.dll": {"fileVersion": "8.0.824.36607"}}}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.26100.57": {"runtime": {"Microsoft.Windows.SDK.NET.dll": {"assemblyVersion": "10.0.26100.38", "fileVersion": "10.0.26100.55"}, "WinRT.Runtime.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.48161"}, "Microsoft.Windows.UI.Xaml.dll": {"assemblyVersion": "10.0.26100.52", "fileVersion": "10.0.26100.55"}}}, "CommunityToolkit.Common/8.2.1": {"runtime": {"lib/net6.0/CommunityToolkit.Common.dll": {"assemblyVersion": "8.2.0.0", "fileVersion": "8.2.1.1"}}}, "CommunityToolkit.WinUI.Controls.Primitives/8.2.250402": {"dependencies": {"CommunityToolkit.WinUI.Extensions": "8.2.250402", "Microsoft.WindowsAppSDK": "1.7.250401001"}, "runtime": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Primitives.dll": {"assemblyVersion": "8.2.0.0", "fileVersion": "8.2.0.0"}}}, "CommunityToolkit.WinUI.Controls.Sizers/8.2.250402": {"dependencies": {"CommunityToolkit.WinUI.Extensions": "8.2.250402", "Microsoft.WindowsAppSDK": "1.7.250401001"}, "runtime": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Controls.Sizers.dll": {"assemblyVersion": "8.2.0.0", "fileVersion": "8.2.0.0"}}}, "CommunityToolkit.WinUI.Extensions/8.2.250402": {"dependencies": {"CommunityToolkit.Common": "8.2.1", "Microsoft.WindowsAppSDK": "1.7.250401001"}, "runtime": {"lib/net8.0-windows10.0.19041/CommunityToolkit.WinUI.Extensions.dll": {"assemblyVersion": "8.2.0.0", "fileVersion": "8.2.0.0"}}}, "LanguageExt.Core/4.4.9": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CSharp": "4.7.0", "System.Diagnostics.Contracts": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Queryable": "4.3.0", "System.Memory": "4.5.5", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/netstandard2.0/LanguageExt.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Web.WebView2/1.0.2903.40": {"native": {"runtimes/win-x86/native/WebView2Loader.dll": {"fileVersion": "1.0.2903.40"}}}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.1742": {}, "Microsoft.WindowsAppSDK/1.7.250401001": {"dependencies": {"Microsoft.Web.WebView2": "1.0.2903.40", "Microsoft.Windows.SDK.BuildTools": "10.0.26100.1742"}, "runtime": {"lib/net6.0-windows10.0.22621.0/Microsoft.InteractiveExperiences.Projection.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.26107.1009"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.WinUI.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.2503"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.AppNotifications.Projection.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Media.Capture.Projection.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.PushNotifications.Projection.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Storage.Projection.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Power.Projection.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.System.Projection.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.Windows.Widgets.Projection.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}, "lib/net6.0-windows10.0.22621.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.7.0.0"}}, "native": {"runtimes/win-x86/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {"fileVersion": "1.7.0.0"}, "runtimes/win-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"fileVersion": "1.7.0.0"}}}, "NLog/5.4.0": {"runtime": {"lib/netstandard2.0/NLog.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.4.0.3182"}}}, "runtime.any.System.Collections/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "runtime.any.System.Globalization/4.3.0": {}, "runtime.any.System.IO/4.3.0": {}, "runtime.any.System.Reflection/4.3.0": {}, "runtime.any.System.Reflection.Extensions/4.3.0": {}, "runtime.any.System.Reflection.Primitives/4.3.0": {}, "runtime.any.System.Resources.ResourceManager/4.3.0": {}, "runtime.any.System.Runtime/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "runtime.any.System.Text.Encoding/4.3.0": {}, "runtime.any.System.Threading.Tasks/4.3.0": {}, "runtime.win.System.Diagnostics.Debug/4.3.0": {}, "runtime.win.System.Runtime.Extensions/4.3.0": {"dependencies": {"System.Private.Uri": "4.3.0"}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Collections": "4.3.0"}}, "System.Configuration.ConfigurationManager/10.0.0-preview.3.25171.5": {"dependencies": {"System.Diagnostics.EventLog": "10.0.0-preview.3.25171.5", "System.Security.Cryptography.ProtectedData": "10.0.0-preview.3.25171.5"}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "System.Diagnostics.Contracts/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.System.Diagnostics.Debug": "4.3.0"}}, "System.Diagnostics.EventLog/10.0.0-preview.3.25171.5": {"runtime": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Globalization": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.any.System.IO": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Memory/4.5.5": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Private.Uri/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection": "4.3.0"}}, "System.Reflection.Emit/4.7.0": {}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.7.0": {}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Extensions": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Primitives": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Resources.ResourceManager": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.any.System.Runtime": "4.3.0"}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.System.Runtime.Extensions": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/10.0.0-preview.3.25171.5": {"runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.17105"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Text.Encoding": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.ValueTuple/4.5.0": {}, "WindowsInput/6.4.1": {"runtime": {"lib/net6.0-windows7.0/WindowsInput.dll": {"assemblyVersion": "6.4.1.0", "fileVersion": "6.4.1.0"}}}, "Microsoft.Web.WebView2.Core.Projection/1.0.2903.40": {"runtime": {"Microsoft.Web.WebView2.Core.Projection.dll": {"assemblyVersion": "1.0.2903.40", "fileVersion": "1.0.2903.40"}}}}}, "libraries": {"HddtodoUI/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x86/8.0.8": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x86/8.0.8": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.26100.57": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "CommunityToolkit.Common/8.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-LWuhy8cQKJ/MYcy3XafJ916U3gPH/YDvYoNGWyQWN11aiEKCZszzPOTJAOvBjP9yG8vHmIcCyPUt4L82OK47Iw==", "path": "communitytoolkit.common/8.2.1", "hashPath": "communitytoolkit.common.8.2.1.nupkg.sha512"}, "CommunityToolkit.WinUI.Controls.Primitives/8.2.250402": {"type": "package", "serviceable": true, "sha512": "sha512-Wx3t1zADrzBWDar45uRl+lmSxDO5Vx7tTMFm/mNgl3fs5xSQ1ySPdGqD10EFov3rkKc5fbpHGW5xj8t62Yisvg==", "path": "communitytoolkit.winui.controls.primitives/8.2.250402", "hashPath": "communitytoolkit.winui.controls.primitives.8.2.250402.nupkg.sha512"}, "CommunityToolkit.WinUI.Controls.Sizers/8.2.250402": {"type": "package", "serviceable": true, "sha512": "sha512-8kWJX+TxUyYbLFPpzoivtYPnSI9kgBGL1dfdsteQSw6Yq69v7mv/Z3emTLHqBJXOi9WKCiq2HNhKXuCH18/ctA==", "path": "communitytoolkit.winui.controls.sizers/8.2.250402", "hashPath": "communitytoolkit.winui.controls.sizers.8.2.250402.nupkg.sha512"}, "CommunityToolkit.WinUI.Extensions/8.2.250402": {"type": "package", "serviceable": true, "sha512": "sha512-rAOYzNX6kdUeeE1ejGd6Q8B+xmyZvOrWFUbqCgOtP8OQsOL66en9ZQTtzxAlaaFC4qleLvnKcn8FJFBezujOlw==", "path": "communitytoolkit.winui.extensions/8.2.250402", "hashPath": "communitytoolkit.winui.extensions.8.2.250402.nupkg.sha512"}, "LanguageExt.Core/4.4.9": {"type": "package", "serviceable": true, "sha512": "sha512-K9VGWkThJkaomifa3zcmwysw1BaSqIZZPZc6trBnJN8u9mpmA/cMMwCWEa/v7bPv/+NnG6PbyIDB7HtxBX7yCQ==", "path": "languageext.core/4.4.9", "hashPath": "languageext.core.4.4.9.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Web.WebView2/1.0.2903.40": {"type": "package", "serviceable": true, "sha512": "sha512-THrzYAnJgE3+cNH+9Epr44XjoZoRELdVpXlWGPs6K9C9G6TqyDfVCeVAR/Er8ljLitIUX5gaSkPsy9wRhD1sgQ==", "path": "microsoft.web.webview2/1.0.2903.40", "hashPath": "microsoft.web.webview2.1.0.2903.40.nupkg.sha512"}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.1742": {"type": "package", "serviceable": true, "sha512": "sha512-ypcHjr4KEi6xQhgClnbXoANHcyyX/QsC4Rky4igs6M4GiDa+weegPo8JuV/VMxqrZCV4zlqDsp2krgkN7ReAAg==", "path": "microsoft.windows.sdk.buildtools/10.0.26100.1742", "hashPath": "microsoft.windows.sdk.buildtools.10.0.26100.1742.nupkg.sha512"}, "Microsoft.WindowsAppSDK/1.7.250401001": {"type": "package", "serviceable": true, "sha512": "sha512-kPsJ2LZoo3Xs/6FtIWMZRGnQ2ZMx9zDa0ZpqRGz1qwZr0gwwlXZJTmngaA1Ym2AHmIa05NtX2jEE2He8CzfhTg==", "path": "microsoft.windowsappsdk/1.7.250401001", "hashPath": "microsoft.windowsappsdk.1.7.250401001.nupkg.sha512"}, "NLog/5.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LwMcGSW3soF3/SL68rlJN3Eh3ktrAPycC3zZR/07OYBPraZUu0bygEC7kIN10lUQgMXT4s84Fi1chglGdGrQEg==", "path": "nlog/5.4.0", "hashPath": "nlog.5.4.0.nupkg.sha512"}, "runtime.any.System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-23g6rqftKmovn2cLeGsuHUYm0FD7pdutb0uQMJpZ3qTvq+zHkgmt6J65VtRry4WDGYlmkMa4xDACtaQ94alNag==", "path": "runtime.any.system.collections/4.3.0", "hashPath": "runtime.any.system.collections.4.3.0.nupkg.sha512"}, "runtime.any.System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sMDBnad4rp4t7GY442Jux0MCUuKL4otn5BK6Ni0ARTXTSpRNBzZ7hpMfKSvnVSED5kYJm96YOWsqV0JH0d2uuw==", "path": "runtime.any.system.globalization/4.3.0", "hashPath": "runtime.any.system.globalization.4.3.0.nupkg.sha512"}, "runtime.any.System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SDZ5AD1DtyRoxYtEcqQ3HDlcrorMYXZeCt7ZhG9US9I5Vva+gpIWDGMkcwa5XiKL0ceQKRZIX2x0XEjLX7PDzQ==", "path": "runtime.any.system.io/4.3.0", "hashPath": "runtime.any.system.io.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hLC3A3rI8jipR5d9k7+f0MgRCW6texsAp0MWkN/ci18FMtQ9KH7E2vDn/DH2LkxsszlpJpOn9qy6Z6/69rH6eQ==", "path": "runtime.any.system.reflection/4.3.0", "hashPath": "runtime.any.system.reflection.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cPhT+Vqu52+cQQrDai/V91gubXUnDKNRvlBnH+hOgtGyHdC17aQIU64EaehwAQymd7kJA5rSrVRNfDYrbhnzyA==", "path": "runtime.any.system.reflection.extensions/4.3.0", "hashPath": "runtime.any.system.reflection.extensions.4.3.0.nupkg.sha512"}, "runtime.any.System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nrm1p3armp6TTf2xuvaa+jGTTmncALWFq22CpmwRvhDf6dE9ZmH40EbOswD4GnFLrMRS0Ki6Kx5aUPmKK/hZBg==", "path": "runtime.any.system.reflection.primitives/4.3.0", "hashPath": "runtime.any.system.reflection.primitives.4.3.0.nupkg.sha512"}, "runtime.any.System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lxb89SMvf8w9p9+keBLyL6H6x/TEmc6QVsIIA0T36IuyOY3kNvIdyGddA2qt35cRamzxF8K5p0Opq4G4HjNbhQ==", "path": "runtime.any.system.resources.resourcemanager/4.3.0", "hashPath": "runtime.any.system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "runtime.any.System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fRS7zJgaG9NkifaAxGGclDDoRn9HC7hXACl52Or06a/fxdzDajWb5wov3c6a+gVSlekRoexfjwQSK9sh5um5LQ==", "path": "runtime.any.system.runtime/4.3.0", "hashPath": "runtime.any.system.runtime.4.3.0.nupkg.sha512"}, "runtime.any.System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+ihI5VaXFCMVPJNstG4O4eo1CfbrByLxRrQQTqOTp1ttK0kUKDqOdBSTaCB2IBk/QtjDrs6+x4xuezyMXdm0HQ==", "path": "runtime.any.system.text.encoding/4.3.0", "hashPath": "runtime.any.system.text.encoding.4.3.0.nupkg.sha512"}, "runtime.any.System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OhBAVBQG5kFj1S+hCEQ3TUHBAEtZ3fbEMgZMRNdN8A0Pj4x+5nTELEqL59DU0TjKVE6II3dqKw4Dklb3szT65w==", "path": "runtime.any.system.threading.tasks/4.3.0", "hashPath": "runtime.any.system.threading.tasks.4.3.0.nupkg.sha512"}, "runtime.win.System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-hHHP0WCStene2jjeYcuDkETozUYF/3sHVRHAEOgS3L15hlip24ssqCTnJC28Z03Wpo078oMcJd0H4egD2aJI8g==", "path": "runtime.win.system.diagnostics.debug/4.3.0", "hashPath": "runtime.win.system.diagnostics.debug.4.3.0.nupkg.sha512"}, "runtime.win.System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RkgHVhUPvzZxuUubiZe8yr/6CypRVXj0VBzaR8hsqQ8f+rUo7e4PWrHTLOCjd8fBMGWCrY//fi7Ku3qXD7oHRw==", "path": "runtime.win.system.runtime.extensions/4.3.0", "hashPath": "runtime.win.system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-rpAIQlaC6r04B8nD//rIDEVdQMAKCeAN9ypDx34gS77Q4ET9+sE+4ApziGvGv5Ve50bT5PoJAt/29JT9LmRhTA==", "path": "system.configuration.configurationmanager/10.0.0-preview.3.25171.5", "hashPath": "system.configuration.configurationmanager.10.0.0-preview.3.25171.5.nupkg.sha512"}, "System.Diagnostics.Contracts/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-eelRRbnm+OloiQvp9CXS0ixjNQldjjkHO4iIkR5XH2VIP8sUB/SIpa1TdUW6/+HDcQ+MlhP3pNa1u5SbzYuWGA==", "path": "system.diagnostics.contracts/4.3.0", "hashPath": "system.diagnostics.contracts.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.EventLog/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-ABk8eOWyQ9Pgmz63EE9hmj0G0eLnWItwM9NV0OGMUOEdseVN1PeA1ryQUIQr5C1odDG2ip4oBTLGSrb5+Z2S3w==", "path": "system.diagnostics.eventlog/10.0.0-preview.3.25171.5", "hashPath": "system.diagnostics.eventlog.10.0.0-preview.3.25171.5.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.Uri/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I4SwANiUGho1esj4V4oSlPllXjzCZDE+5XXso2P03LW2vOda2Enzh8DWOxwN6hnrJyp314c7KuVu31QYhRzOGg==", "path": "system.private.uri/4.3.0", "hashPath": "system.private.uri.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "path": "system.reflection.emit.lightweight/4.7.0", "hashPath": "system.reflection.emit.lightweight.4.7.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/10.0.0-preview.3.25171.5": {"type": "package", "serviceable": true, "sha512": "sha512-qsIqM920BE+fPW5RDNUPx2fx3Uk6ZCi7UiUqpGKIWUdt8aTCxdemeh4NaWFQhwd61JDnUsfNFeY8yMYbNzS20g==", "path": "system.security.cryptography.protecteddata/10.0.0-preview.3.25171.5", "hashPath": "system.security.cryptography.protecteddata.10.0.0-preview.3.25171.5.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "WindowsInput/6.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-wfg9uu5xDvBcaNqvR1ZTFQqBf4bKzkxdd5WviTZPy4cAywEzLzofuYswZB3nGIEnnlwGLBGSgrNOEZH/w8JI9w==", "path": "windowsinput/6.4.1", "hashPath": "windowsinput.6.4.1.nupkg.sha512"}, "Microsoft.Web.WebView2.Core.Projection/1.0.2903.40": {"type": "reference", "serviceable": false, "sha512": ""}}, "runtimes": {"win-x86": ["win", "any", "base"]}}