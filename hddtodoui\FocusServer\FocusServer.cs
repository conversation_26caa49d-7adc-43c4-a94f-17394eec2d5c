using System;
using System.Net;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

using HddtodoUI.TaskTomatoManager;
using LanguageExt;
using static LanguageExt.Prelude; 

namespace HddtodoUI.FocusServer
{
    public class FocusServer : IDisposable
    {
        private readonly HttpListener _httpListener;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly Thread _serverThread;
        //private string _currentTask = "Stay focused on your work!";
        private bool _isRunning = false;

        private static readonly Lazy<FocusServer> _instance = new Lazy<FocusServer>(() => new FocusServer());
        public static FocusServer Instance => _instance.Value;

        private string getCurrentTask()
        {
            var taskOpt = CurrentStatusHolder.getCurrentStatusHolder().getCurrentTomatoTaskOption();
            
            return taskOpt.Bind(task =>
            {
                return task.getTaskStatus() == TomatoTaskStaus.taskUnCompletedAndTomatoOngoing
                    ? task.getTask().Title
                    : Option<string>.None;
            }).IfNoneUnsafe(() => "当前没有任务正在执行。。。");
        }

        private FocusServer()
        {
            _httpListener = new HttpListener();
            _cancellationTokenSource = new CancellationTokenSource();
            _serverThread = new Thread(StartServer);
            _httpListener.Prefixes.Add("http://localhost:9009/");
        }

        public void Start()
        {
            if (!_isRunning)
            {
                _isRunning = true;
                _serverThread.Start();
                Console.WriteLine("Focus Server started on http://localhost:9009");
            }
        }

        public void Stop()
        {
            if (_isRunning)
            {
                _isRunning = false;
                _cancellationTokenSource.Cancel();
                if (_httpListener.IsListening)
                {
                    _httpListener.Stop();
                }
                
                _serverThread.Join();
                Console.WriteLine("Focus Server stopped");
            }
        }
        private void StartServer()
        {
            try
            {
                _httpListener.Start();

                while (_isRunning)
                {
                    try
                    {
                        HttpListenerContext context = _httpListener.GetContext();
                        ProcessRequest(context);
                    }
                    catch (HttpListenerException)
                    {
                        break; // Server stopped
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error processing request: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Server error: {ex.Message}");
            }
        }

        private void ProcessRequest(HttpListenerContext context)
        {
            try
            {
                // Handle CORS preflight request
                if (context.Request.HttpMethod == "OPTIONS")
                {
                    context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                    context.Response.Headers.Add("Access-Control-Allow-Methods", "GET, OPTIONS");
                    context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");
                    context.Response.StatusCode = 200;
                    context.Response.Close();
                    return;
                }

                // Handle actual request
                if (context.Request.Url.LocalPath == "/task")
                {
                    // Add CORS headers
                    context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                    context.Response.Headers.Add("Content-Type", "application/json; charset=utf-8");

                    // Create JSON response
                    string json = System.Text.Json.JsonSerializer.Serialize(new { currentTask = getCurrentTask() });
                    byte[] buffer = Encoding.UTF8.GetBytes(json);

                    // Send response
                    context.Response.ContentLength64 = buffer.Length;
                    context.Response.OutputStream.Write(buffer, 0, buffer.Length);
                }
                else
                {
                    context.Response.StatusCode = 404;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error handling request: {ex.Message}");
                context.Response.StatusCode = 500;
            }
            finally
            {
                context.Response.Close();
            }
        }

        public void Dispose()
        {
            Stop();
            _cancellationTokenSource.Dispose();
            _httpListener.Close();
        }
    }
}
