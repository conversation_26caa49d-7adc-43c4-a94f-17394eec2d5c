using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using Windows.ApplicationModel.DataTransfer; // Required for DataPackageOperation
using Windows.Foundation;
using Windows.Graphics.Display;
using HddtodoUI.BackendModels;
using HddtodoUI.BackendModels.BackendStore;
using HddtodoUI.BackendModels.StoreFactory;
using HddtodoUI.BackendModels.TaskFactory;
using HddtodoUI.Models;
using HddtodoUI.Services;
using HddtodoUI.TaskTomatoManager;
using HddtodoUI.TaskTomatoManager.Constants;
using HddtodoUI.UICoordinator;
using HddtodoUI.Utilities;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Input;
using Microsoft.UI.Xaml.Media;

namespace HddtodoUI.Controls
{
    public sealed partial class TaskCategoriesTreeViewPanel : UserControl, INotifyPropertyChanged
    {
        private bool _isAddListNameValid;
        private bool _isEditMode = false; // 添加标志，用于区分新建和编辑模式
        private String _editParentKey = null;
        private TaskCategoryViewObject _currentEditingTaskList = null; // 当前正在编辑的任务列表

        // 构造函数
        public TaskCategoriesTreeViewPanel()
        {
            this.InitializeComponent();
            InitializeCollections();
            
        }
        
        
        // 属性
        private ObservableCollection<TaskCategoryViewObject> SystemCategories { get; set; }
        private ObservableCollection<TaskCategoryViewObject> UserCategories { get; set; }

        public bool IsAddListNameValid
        {
            get => _isAddListNameValid;
            set
            {
                if (_isAddListNameValid != value)
                {
                    _isAddListNameValid = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(IsAddListNameValid)));
                }
            }
        }

        // 添加 PropertyChanged 事件
        public event PropertyChangedEventHandler PropertyChanged;

        // 事件定义，用于与 MainView 交互
        public event EventHandler<TaskCategoryViewObject> CategorySelected;
        public event EventHandler<TaskCategoryViewObject> CategoryTreeItemSelected;

        // 初始化集合
        private void InitializeCollections()
        {
            SystemCategories = new ObservableCollection<TaskCategoryViewObject>();
            UserCategories = new ObservableCollection<TaskCategoryViewObject>();

            // 设置数据源
            SystemTasksListView.ItemsSource = SystemCategories;
            UserCategoriesTreeView.ItemsSource = UserCategories;
        }

        // 公共方法：初始化系统类别
        public void InitializeSystemCategories(IEnumerable<TaskCategoryViewObject> categories)
        {
            SystemCategories.Clear();
            foreach (var category in categories)
            {
                SystemCategories.Add(category);
            }
        }

        // 公共方法：初始化用户类别
        public void InitializeUserCategories(IEnumerable<TaskCategoryViewObject> categories)
        {
            UserCategories.Clear();
            CheckUserCategoryHasLazyLoadedChildren(categories);
            foreach (var category in categories)
            {
                UserCategories.Add(category);
            }
            
        }

        private void CheckUserCategoryHasLazyLoadedChildren( IEnumerable<TaskCategoryViewObject> categories)
        {
            foreach (var userCategory in categories)
            {
                if (userCategory.HasChildren)
                {
                    // var fakeChild =new TaskCategoryViewObject
                    // {
                    //     Name = "fakechild", 
                    //     Key = SpecialTaskListConstants.Deleted, 
                    //     TaskCount = SystemCategoryManager.getDeletedTasksCount(),
                    //     IconName = "\uE74D",
                    //     IsSystemCategory = true,
                    //     HasChildren = false,
                    //     IsChildrenLoaded = false,
                    //     Children = new ObservableCollection<TaskCategoryViewObject>()
                    // };
                    // userCategory.Children.Add(fakeChild);
                    userCategory.IsChildrenLoaded = false;
                }
            }
        }


        // 事件处理程序：系统列表选择改变
        private void TasksListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var listView = sender as ListView;
            if (listView.SelectedItem is TaskCategoryViewObject selectedCategory)
            {
                // 如果从系统列表选择，取消树视图的选择
                if (UserCategoriesTreeView.SelectedItem != null)
                {
                    UserCategoriesTreeView.SelectedItem = null;
                }

                // 触发事件
                CategorySelected?.Invoke(this, selectedCategory);
            }
        }
        
        // 事件处理程序：用户分类树视图选择改变
        private void UserCategoriesTreeView_SelectionChanged(object sender, TreeViewSelectionChangedEventArgs e)
        {
            var treeView = sender as TreeView;
            if (treeView.SelectedItem is TaskCategoryViewObject selectedCategory)
            {
                // 如果从树视图选择，取消系统列表的选择
               
            }
        }
        
        
        // 事件处理程序：树视图项被调用
        private void UserCategoriesTreeView_ItemInvoked(TreeView sender, TreeViewItemInvokedEventArgs args)
        {
            // 当项被点击时，可以在这里处理
            if (args.InvokedItem is TaskCategoryViewObject category)
            {
                // 可以在这里添加额外的处理逻辑
                if (SystemTasksListView.SelectedItem != null)
                {
                    SystemTasksListView.SelectedItem = null;
                }
                // 触发事件
                if (category.IsSystemCategory)
                    CategorySelected?.Invoke(this, category);
                else
                    CategoryTreeItemSelected?.Invoke(this, category);
                
                var node = FindNodeByKey(sender,category.Key);
                if(category.HasChildren)
                    node.IsExpanded = true;
            }

         
            
        }
        
        // 事件处理程序：树视图项展开
        private async void UserCategoriesTreeView_Expanding(TreeView sender, TreeViewExpandingEventArgs args)
        {
            if (args.Item is TaskCategoryViewObject category && category.HasChildren && !category.IsChildrenLoaded)
            {
                // 懒加载子分类
                category.IsChildrenLoaded = true;
                await LoadChildCategories(category);
            }
        }
        
        private void UserCategoriesTreeView_OnCollapsed(TreeView sender, TreeViewCollapsedEventArgs args)
        {
            if (args.Item is TaskCategoryViewObject category)
            {
                //这里设置成false,则expanding哪里就会重新loadchildcategories
                category.IsChildrenLoaded = false;
            }
        }
        
      
        
        // 加载子分类
        private async Task LoadChildCategories(TaskCategoryViewObject parentCategory)
        {
            try
            {
                // 获取子分类
                var childCategories = await Task.Run(() =>
                {
                    var store = StoreFactoryHolder.getTaskCategoryStore();
                    return store.GetUncompleteTaskCategoriesWithCount(UserInfoHolder.getUserId(), parentCategory.Key);
                });
                
                // 清空现有子分类
                UserCategoriesTreeView.Collapsed -= UserCategoriesTreeView_OnCollapsed;
                parentCategory.Children.Clear();
                UserCategoriesTreeView.Collapsed += UserCategoriesTreeView_OnCollapsed;

                
                var categories = new List<TaskCategoryViewObject>();
                
                foreach (var categoryWithCount in childCategories)
                {
                    categories.Add(TaskCategoryViewObject.GetFrom(categoryWithCount.Category, categoryWithCount.TaskCount, categoryWithCount.SubcategoryCount));
                }
                
                CheckUserCategoryHasLazyLoadedChildren(categories);
                
                foreach (var category in categories)
                {
                    parentCategory.Children.Add(category);
                }
                
                
                LogService.Instance.Debug("load children of category: " + parentCategory.Name);
            }
            catch (Exception ex)
            {
                // 处理异常
                System.Diagnostics.Debug.WriteLine($"加载子分类失败: {ex.Message}");
            }
        }

        // 事件处理程序：添加列表按钮点击
        private async void AddListButton_Click(object sender, RoutedEventArgs e)
        {
            // 设置为新建模式
            _isEditMode = false;
            _currentEditingTaskList = null;
            _editParentKey = null;
            
            // 设置对话框标题和按钮文本
            AddListDialog.Title = "添加新列表";
            AddListDialog.PrimaryButtonText = "添加";
            
            // 重置对话框状态
            ListNameTextBox.Text = string.Empty;
            ListDueDatePicker.Date = null;
            ListPriorityComboBox.SelectedIndex = 1;
            IsAddListNameValid = false;
            
            // 设置XamlRoot
            AddListDialog.XamlRoot = this.XamlRoot;

            await AddListDialog.ShowAsync();
        }

        // 事件处理程序：列表名称文本框内容改变
        private void ListNameTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            IsAddListNameValid = !string.IsNullOrWhiteSpace(ListNameTextBox.Text);
        }

        // 事件处理程序：添加列表对话框确定按钮点击
        private async void AddListDialog_PrimaryButtonClick(ContentDialog sender, ContentDialogButtonClickEventArgs args)
        {
            if (_isEditMode && _currentEditingTaskList != null)
            {
                // 编辑模式：更新现有任务列表
                
                // 获取原始任务列表
                var originalList = StoreFactoryHolder.getTaskCategoryStore()
                    .GetTaskCategoryByKey(_currentEditingTaskList.Key, UserInfoHolder.getUserId());
                
                if (originalList != null)
                {
                    // 更新任务列表
                    originalList.Name = ListNameTextBox.Text.Trim();
                    
                    // 更新到期时间
                    if (ListDueDatePicker.Date.HasValue)
                    {
                        originalList.SetDueTime(ListDueDatePicker.Date.Value.DateTime);
                    }
                    else
                    {
                        originalList.CategoryDueTime = null;
                    }
                    
                    // 保存更改
                    var savedTaskList = await StoreFactoryHolder.getTaskCategoryStore()
                        .UpdateTaskCategory(
                            originalList.Key, 
                            UserInfoHolder.getUserId(),
                            UpdateValue<DateTime?>.SetTo(originalList.CategoryCompleteTime),
                            UpdateValue<DateTime?>.SetTo(originalList.CategoryDueTime),
                            originalList.Name, 
                            originalList.IsHide,
                            (int?)originalList.CategoryOrder);
                    
                    // 更新UI
                    // 在整棵树中查找对应的分类
                    var changedCategory = FindCategoryInTree(savedTaskList.Key);
                    
                    if (changedCategory != null)
                    {
                        // 创建新的视图对象
                        // var updatedViewObject = TaskCategoryViewObject.GetFrom(savedTaskList);
                        // updatedViewObject.TaskCount = changedCategory.TaskCount; // 保持任务计数
                        // updatedViewObject.SubCategoryCount = changedCategory.SubCategoryCount;
                        //
                        // // 更新树结构中的节点
                        // UpdateCategoryInTree(savedTaskList.Key, updatedViewObject);
                        
                        UpdateCategoryInTree(savedTaskList.Key, TaskCategoryViewObject.GetFrom(savedTaskList));
                        
                        // 如果当前选中的是被编辑的项，更新选择
                        if (UserCategoriesTreeView.SelectedItem == _currentEditingTaskList)
                        {
                            // 重新查找更新后的节点
                            var updatedNode = FindCategoryInTree(savedTaskList.Key);
                            if (updatedNode != null)
                            {
                                UserCategoriesTreeView.SelectedItem = updatedNode;
                                // 触发选择事件
                                CategorySelected?.Invoke(this, updatedNode);
                            }
                        }
                    }
                    
                    TheUICoordinator.Instance.OnTaskListAdded();
                }
            }
            else
            {
                // 新建模式：创建新的任务列表
                var newList = new TaskListViewObject
                {
                    Name = ListNameTextBox.Text.Trim(),
                    DueDate = ListDueDatePicker.Date?.DateTime,
                    Priority = (TaskPriority)ListPriorityComboBox.SelectedIndex,
                    IsSystemList = false,
                    TaskCount = 0
                };

                TaskCategory newCreated = await StoreFactoryHolder.getTaskCategoryStore()
                    .CreateTaskCategory(newList.Name, UserInfoHolder.getUserId(), _editParentKey, newList.DueDate);

                if (newCreated.ParentCategoryKey == null)
                {
                    UserCategories.Insert(UserCategories.Count-1,TaskCategoryViewObject.GetFrom(newCreated));
                }
                else
                {
                    // 创建新的分类视图对象
                    var newCategoryView = TaskCategoryViewObject.GetFrom(newCreated, 0, 0);
                    
                    var parentCategory = FindCategoryInTree(_editParentKey);
                    
                    // 如果父分类的子分类尚未加载，则初始化子分类集合
                    if (parentCategory.Children == null)
                    {
                        parentCategory.Children = new ObservableCollection<TaskCategoryViewObject>();
                    }
                    
                    // 添加新分类到父分类的子项
                    parentCategory.Children.Add(newCategoryView);
                    
                    // 更新父分类的 HasChildren 状态
                    parentCategory.HasChildren = true;
                    
                    // 展开父分类以显示新添加的子分类
                    var allNodes = UserCategoriesTreeView.RootNodes
                        .SelectMany(root => GetAllDescendants(root));
                    
                    var treeViewItem = allNodes
                        .FirstOrDefault(n => n.Content is TaskCategoryViewObject category && 
                                          category.Key == parentCategory.Key);
                    
                    if (treeViewItem != null)
                    {
                        treeViewItem.IsExpanded = true;
                    }
                    
                    //下面2句会导致程序异常退出
                    // 选中新创建的分类
                    //UserCategoriesTreeView.SelectedItem = newCategoryView;
                    
                    // 触发选择事件
                    //CategoryTreeItemSelected?.Invoke(this, newCategoryView);
                }
               
            }
            
            // 重置模式
            _isEditMode = false;
            _currentEditingTaskList = null;
            _editParentKey = null;
        }

        // 事件处理程序：添加列表对话框取消按钮点击
        private void AddListDialog_CloseButtonClick(ContentDialog sender, ContentDialogButtonClickEventArgs args)
        {
            // 重置模式
            _isEditMode = false;
            _currentEditingTaskList = null;
            
            // 重置对话框标题和按钮文本
            AddListDialog.Title = "添加新列表";
            AddListDialog.PrimaryButtonText = "添加";
        }
        
        // 事件处理程序：添加新任务菜单项点击
        private async void AddTaskMenuItem_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuFlyoutItem;
            if (menuItem != null)
            {
                var taskList = menuItem.DataContext as TaskCategoryViewObject;
                if (taskList != null)
                {
                    
                    var dialog = new QuickAddTaskDialog();

                    // 创建 ContentDialog 来显示 QuickAddTaskDialog
                    ContentDialog contentDialog = new ContentDialog
                    {
                        //Title = $"添加任务到{taskList.Name}",
                        Content = dialog,
                        PrimaryButtonText = "", // 不使用内置按钮，而是使用 QuickAddTaskDialog 的按钮
                        CloseButtonText = "",
                        XamlRoot = this.XamlRoot
                    };
                    
                    
                   
                    dialog.HideTaskListComboBox();

                    // 创建一个标志，用于跟踪任务是否已保存
                    bool taskSaved = false;

                    // 订阅事件
                    dialog.TaskSaved += (s, taskVo) =>
                    {
                        // 设置任务的类别
                        taskVo.Category = taskList.Key;

                        var list = StoreFactoryHolder.getTaskCategoryStore()
                            .GetTaskCategoryByKey(taskVo.Category, UserInfoHolder.getUserId());
                        // 添加任务到集合
                        var task = TTaskFactory.CreateTaskInTaskList(taskVo.Title, taskVo.DueDate,taskVo.Priority, list);

                        TaskUICoordinatorFactory.Instance(task).OnTaskAdded(task, list);
                        
                        if (dialog.IsStartTask)
                        {
                            var ttm=TaskTomatoManagerFactory.GetTomatoTaskManager(task);
                            TaskUICoordinatorFactory.Instance(task).StartOrPauseButtonClick(ttm);
                        }

                        // 标记任务已保存
                        taskSaved = true;

                        // 关闭对话框
                        contentDialog.Hide();
                    };

                    dialog.DialogCancelled += (s, e) =>
                    {
                        // 关闭对话框
                        contentDialog.Hide();
                    };

                    contentDialog.Resources["ContentDialogMaxWidth"] = 2000;
                    contentDialog.Resources["ContentDialogMaxHeight"] = 1000;
                    dialog.SetTitleBlockText($"添加任务到{taskList.Name}");
                    // 显示对话框
                    await contentDialog.ShowAsync();
                    
                }
            }
        }

        // 事件处理程序：编辑标题菜单项点击
        private async void EditTitleMenuItem_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuFlyoutItem;
            if (menuItem != null)
            {
                var taskList = menuItem.DataContext as TaskCategoryViewObject;
                if (taskList != null && taskList.Name != "收集箱")
                {
                    // 获取原始任务列表
                    var originalList = StoreFactoryHolder.getTaskCategoryStore()
                        .GetTaskCategoryByKey(taskList.Key, UserInfoHolder.getUserId());
                    
                    if (originalList == null)
                    {
                        return;
                    }
                    
                    // 设置为编辑模式
                    _isEditMode = true;
                    _currentEditingTaskList = taskList;
                    
                    // 设置对话框标题和按钮文本
                    AddListDialog.Title = "编辑任务列表";
                    AddListDialog.PrimaryButtonText = "保存";
                    
                    // 设置对话框内容
                    ListNameTextBox.Text = taskList.Name;
                    ListDueDatePicker.Date = taskList.DueDate;
                    
                    // 设置优先级
                    ListPriorityComboBox.SelectedIndex = (int)taskList.Priority;
                    
                    // 启用确定按钮
                    IsAddListNameValid = true;
                    
                    // 设置XamlRoot
                    AddListDialog.XamlRoot = this.XamlRoot;
                    
                    // 显示对话框
                    var result = await AddListDialog.ShowAsync();
                    
                    // 重置为新建模式
                    _isEditMode = false;
                    _currentEditingTaskList = null;
                    
                    // 重置对话框标题和按钮文本
                    AddListDialog.Title = "添加新列表";
                    AddListDialog.PrimaryButtonText = "添加";
                }
            }
        }
        
        // 管理已完成任务列表按钮点击事件
        private void ManageCompletedListsButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new CompletedTaskListsDialog();
            
            // 创建 ContentDialog 来显示 CompletedTaskListsDialog
            ContentDialog contentDialog = new ContentDialog
            {
                Title = "管理已完成的任务分类",
                Content = dialog,
                PrimaryButtonText = "", // 不使用内置按钮，而是使用 CompletedTaskListsDialog 的按钮
                CloseButtonText = "",
                XamlRoot = this.XamlRoot
            };
            
            contentDialog.Resources["ContentDialogMaxWidth"] = 2000;
            contentDialog.Resources["ContentDialogMaxHeight"] = 1000;
            // 订阅事件
            dialog.DialogClosed += (s, args) =>
            {
                contentDialog.Hide();
            };
            
            dialog.TaskCategorySetIncomplete += (s, taskCategory) =>
            {
                
                // 获取完整的TaskList对象
                var category = StoreFactoryHolder.getTaskCategoryStore().GetTaskCategoryByKey(taskCategory.Key, UserInfoHolder.getUserId());
                
                // 设置为未完成
                category.CategoryCompleteTime = null;
                
                var order = UserCategories.Count;
                category.CategoryOrder = order;
                
                // 保存更改
                StoreFactoryHolder.getTaskCategoryStore().UpdateTaskCategory(taskCategory.Key,
                    UserInfoHolder.getUserId(), UpdateValue<DateTime?>.SetTo(null),null,taskCategory.Name,taskCategory.IsDeleted,order);
                
                int taskCount = StoreFactoryHolder.getTaskCategoryStore().GetCategoryUncompleteTaskCount(taskCategory.Key,UserInfoHolder.getUserId());
                taskCategory.TaskCount = taskCount;
                
                UserCategories.Insert(UserCategories.Count-1,taskCategory);

                contentDialog.Hide();
                    
              
                
            };
            
            // 显示对话框
            contentDialog.ShowAsync();
        }

        private void UserCategoriesTreeItem_RightTapped(object sender, RightTappedRoutedEventArgs e)
        {
            var treeViewItem = sender as TreeViewItem;
            if (treeViewItem != null)
            {
                // 设置右键菜单的上下文
                treeViewItem.ContextFlyout.ShowAt(treeViewItem);
            }
        }
     

       
        // 事件处理程序：添加子分类菜单项点击
        private async void AddSubCategoryMenuItem_Click(object sender, RoutedEventArgs e)
        {
           var menuItem = sender as MenuFlyoutItem;
           if (menuItem?.DataContext is TaskCategoryViewObject parentCategory)
           {
               // 设置为新建子分类模式
               _isEditMode = false;
               _currentEditingTaskList = null;
               _editParentKey = parentCategory.Key;
               
               // 设置对话框标题和按钮文本
               AddListDialog.Title = "添加子分类";
               AddListDialog.PrimaryButtonText = "添加";
               
               // 重置对话框状态
               ListNameTextBox.Text = string.Empty;
               ListDueDatePicker.Date = null;
               ListPriorityComboBox.SelectedIndex = 1;
               IsAddListNameValid = false;
               
               // 设置XamlRoot
               AddListDialog.XamlRoot = this.XamlRoot;
       
               // 显示对话框
                await AddListDialog.ShowAsync();
               
             
           }
       }
       
        // 事件处理程序：完成分类菜单项点击
        private void CompleteThisCategoryMenuItem_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuFlyoutItem;
            if (menuItem?.DataContext is TaskCategoryViewObject category)
            {
                // 完成分类的逻辑
                
                category.CategoryCompleteTime = DateTime.Now;
                
                StoreFactoryHolder.getTaskCategoryStore()
                    .UpdateTaskCategory(category.Key, UserInfoHolder.getUserId(), UpdateValue<DateTime?>.SetTo(category.CategoryCompleteTime));
                
             
                
                var parentCollection = FindParentCollection(category);
                if (parentCollection != null)
                {
                    parentCollection.Remove(category);
                }
                else
                {
                    // 如果找不到父集合，直接从根集合中移除
                    UserCategories.Remove(category);
                }
                
                TheUICoordinator.Instance.OnTaskCategoryCompleted(category);
                
            }
        }

        // 右键菜单：修改截至时间
        private async void ChangeDueTimeMenuItem_Click(object sender, RoutedEventArgs e)
        {
            // 获取当前右键的 TreeViewItem 的 DataContext
            var menuItem = sender as MenuFlyoutItem;
            if (!(menuItem?.DataContext is TaskCategoryViewObject categoryVO))
                return;
            
            var dueDatePicker = new CalendarDatePicker
            {
                Date = categoryVO.DueDate,
                MinDate = DateTimeOffset.Now.AddYears(-10),
                MaxDate = DateTimeOffset.Now.AddYears(10),
                PlaceholderText = "选择新的截至日期"
            };

            var changeDueTimeDialog = new ContentDialog
            {
                Title = "修改截至时间",
                Content = dueDatePicker,
                PrimaryButtonText = "确定",
                CloseButtonText = "取消",
                XamlRoot = this.XamlRoot
            };

            var result = await changeDueTimeDialog.ShowAsync();
            if (result == ContentDialogResult.Primary)
            {
                DateTime? newDueTime = null;
                if (dueDatePicker.Date != null)
                {
                    newDueTime = dueDatePicker.Date.Value.DateTime;
                }

                if (dueDatePicker.Date == null)
                {
                    newDueTime = null;
                }
                
                if (categoryVO.DueDate != newDueTime)
                {
                    await StoreFactoryHolder.getTaskCategoryStore().UpdateTaskCategory(categoryVO.Key, UserInfoHolder.getUserId(), UpdateValue<DateTime?>.NoChange, UpdateValue<DateTime?>.SetTo(newDueTime));
                    var updated = StoreFactoryHolder.getTaskCategoryStore().GetTaskCategoryByKey(categoryVO.Key, UserInfoHolder.getUserId());
                    // 更新界面
                    categoryVO.DueDate = updated.CategoryDueTime;
                    TheUICoordinator.Instance.UpdateUserCategory(categoryVO.Key, TaskCategoryViewObject.GetFrom(updated));
                    await TheUICoordinator.Instance.ReloadPlannedProjectPanelAsync();
                }
            }
        }

        private void ScrollViewer_OnViewChanged(object sender, ScrollViewerViewChangedEventArgs e)
        {
            
            var scrollView = sender as ScrollViewer;
            if (scrollView == null) return;

            // 当前滚动位置
            double verticalOffset = scrollView.VerticalOffset;
            // 可滚动的总高度
            double scrollableHeight = scrollView.ScrollableHeight;

            //LogService.Instance.Debug( $"verticalOffset: {verticalOffset}, scrollableHeight: {scrollableHeight}");
            // 判断是否显示上方提示
            if (verticalOffset > 10)
            {
                TopHint.Visibility = Visibility.Visible;
            }
            else
            {
                TopHint.Visibility = Visibility.Collapsed;
            }

            // 判断是否显示下方提示
            if (verticalOffset < scrollableHeight-10)
            {
                BottomHint.Visibility = Visibility.Visible;
            }
            else
            {
                BottomHint.Visibility = Visibility.Collapsed;
            }
        }
        
        private void FrameworkElement_OnLoaded(object sender, RoutedEventArgs e)
        {
            ScrollViewer_OnViewChanged(sender, null);
        }
        
        public void SetTaskCategoryListViewCanReOrder()
        {
            //UserCategoriesTreeView.CanReorderItems = true;
        }

        public void SetTaskCategoryListViewCanotReOrder()
        {
            //UserCategoriesTreeView.ReorderMode = ListViewReorderMode.Disabled;
           // UserCategoriesTreeView.CanReorderItems = false;
        }
        
        private ObservableCollection<TaskCategoryViewObject> beforeDropedUserCategories = null;
        private void UserCategoriesTreeView_OnDragItemsStarting(TreeView sender, TreeViewDragItemsStartingEventArgs args)
        {
            if (args.Items.Any())
            {
                var draggedItem = args.Items.FirstOrDefault();
                // Assuming the item in the TreeView is a TaskCategory object or can be cast to it.
                // If you are using a ViewModel, you might need to cast to your ViewModel and then access the TaskCategory.
                var draggedCategory = draggedItem as TaskCategoryViewObject; // Using TaskCategoryViewObject, ensure this is the correct data type for your TreeView items

                if (draggedCategory != null)
                {
                    // SUCCESS: 'draggedCategory' is the TaskCategory of the item being dragged.
                    // You can store this or use it as needed. For example, to identify the source.
                    // For this example, we'll primarily use information from DragItemsCompleted.
                    LogService.Instance.Debug($"Drag Starting: {draggedCategory.Name}"); // Example usage

                    if (draggedCategory.IsSystemCategory || draggedCategory.Name == "回收站" || draggedCategory.Name == "收集箱")
                    {
                        args.Cancel = true;
                        args.Data.RequestedOperation = DataPackageOperation.None;
                        return;
                    }
                    

                    // var node = FindNodeByKey(sender, draggedCategory.Key);
                    // dragStartIndex = sender.RootNodes.IndexOf(node); // Get the index of the node()

                    beforeDropedUserCategories = new ObservableCollection<TaskCategoryViewObject>();
                    if (UserCategories != null) // Add null check for safety
                    {
                        foreach (var category in UserCategories)
                        {
                            beforeDropedUserCategories.Add(DeepCopyCategory(category));
                        }
                    }
                }
            }
            args.Data.RequestedOperation = DataPackageOperation.Move;
        }
        
        private void UserCategoriesTreeView_OnDragOver(object sender, DragEventArgs e)
        {
            LogService.Instance.Debug(e.AllowedOperations.ToString());
           
            var treeView = sender as TreeView;
            Point point = e.GetPosition(treeView);
            LogService.Instance.Debug($"Dragging over position: {point}");
            TreeViewNode targetNode = null;
            
            // double scale = DisplayInformation.GetForCurrentView().RawPixelsPerViewPixel;
            // Point scaledPosition = new Point(point.X / scale, point.Y / scale);
            
            foreach (var node in treeView.RootNodes)
            {
                 targetNode = FindNodeAtPosition(treeView, node, point);
                if (targetNode != null)
                {
                    // 找到目标节点
                    break;
                }
            }
           
            if (targetNode != null)
            {
                // 现在 targetNode 就是鼠标悬停在其上方的 TreeViewNode
                var categoryContent = targetNode.Content as TaskCategoryViewObject;
                if (categoryContent != null)
                {
                    LogService.Instance.Debug($"Dragging over node: {categoryContent.Name} (Key: {categoryContent.Key})");
                    
                    // 您可以在这里根据 targetNode 的内容来设置 e.AcceptedOperation
                    // 例如: e.AcceptedOperation = DataPackageOperation.Move; 
                    // 默认情况下，如果拖拽源允许Move，并且这里不拒绝，通常会显示Move光标
                    e.DataView.Properties.TryGetValue("Item", out var item );
                    if (item is TodoTaskViewObject todoTaskViewObject)
                    {
                        if (categoryContent.Key != todoTaskViewObject.Category)
                        {
                            e.AcceptedOperation = DataPackageOperation.Move;
                            var taskTitle = todoTaskViewObject.Title.Substring(0, Math.Min(5, todoTaskViewObject.Title.Length))+"...";
                            var categoryTitle = categoryContent.Name;
                            e.DragUIOverride.Caption = $"把: {taskTitle} 移动到列表: {categoryTitle}"; // 设置拖动时的提示文本
                            e.DragUIOverride.IsCaptionVisible = true;      // 确保提示可见
                            e.DragUIOverride.IsGlyphVisible = false;        // 显示默认拖动图标
                            e.DragUIOverride.IsContentVisible = true;
                        }
                        else
                        {
                            e.AcceptedOperation = DataPackageOperation.None;
                        }
                
                    
                    }
                }
                else
                {
                    LogService.Instance.Debug($"Dragging over a node, but content is not TaskCategoryViewObject or is null.");
                }
                
            }
            else
            {
                LogService.Instance.Debug("Dragging over TreeView, but not over a specific node item.");
                // 如果不是在任何特定节点上（例如，在 TreeView 的空白区域），targetNode 会是 null
                // 此时可以决定是否允许在根级别放置等
            }

            e.Handled = false;
            
        }
        
        
        private void UserCategoriesTreeView_OnDragEnter(object sender, DragEventArgs e)
        {
            LogService.Instance.Debug(e.AllowedOperations.ToString());
        }
        
        private void UserCategoriesTreeView_OnDragStarting(UIElement sender, DragStartingEventArgs e)
        {
            LogService.Instance.Debug(e.AllowedOperations.ToString());
        }
        
        private void UserCategoriesTreeView_OnDrop(object sender, DragEventArgs e)
        {
            LogService.Instance.Debug(e.AllowedOperations.ToString());
            
             
            var treeView = sender as TreeView;
            Point point = e.GetPosition(treeView);
            LogService.Instance.Debug($"Dragging over position: {point}");
            TreeViewNode targetNode = null;
            
            // double scale = DisplayInformation.GetForCurrentView().RawPixelsPerViewPixel;
            // Point scaledPosition = new Point(point.X / scale, point.Y / scale);
            
            foreach (var node in treeView.RootNodes)
            {
                targetNode = FindNodeAtPosition(treeView, node, point);
                if (targetNode != null)
                {
                    // 找到目标节点
                    break;
                }
            }

            if (targetNode != null)
            {
                e.DataView.Properties.TryGetValue("Item", out var item );
                if (item is TodoTaskViewObject todoTaskViewObject)
                {
                    var categoryContent = targetNode.Content as TaskCategoryViewObject;

                    if (todoTaskViewObject.Category != categoryContent.Key)
                    {
                        var list = StoreFactoryHolder.getTaskCategoryStore().GetTaskCategoryByKey(categoryContent.Key,UserInfoHolder.getUserId());
                        StoreFactoryHolder.getTaskStore().moveTaskToOtherTaskList(todoTaskViewObject.Task,list,UserInfoHolder.getUserId());
                        
                        // 刷新TomatoTaskManager中的任务数据缓存
                        var tomatoTaskManager = TaskTomatoManagerFactory.GetTomatoTaskManager(todoTaskViewObject.TaskID);
                        if (tomatoTaskManager != null)
                        {
                            tomatoTaskManager.RefreshTaskData();
                        }
             
                        TaskUICoordinatorFactory.Instance(todoTaskViewObject.Task).OnTaskDragMoveToCategory(todoTaskViewObject,list);
                        
                    }
                    else
                    {
                        e.AcceptedOperation = DataPackageOperation.None;
                    }
                    
                    e.Handled = true;
                }
            
            }
        }
        
        private void UserCategoriesTreeView_OnDragLeave(object sender, DragEventArgs e)
        {
            LogService.Instance.Debug(e.AllowedOperations.ToString());
        }
        
        private async void UserCategoriesTreeView_OnDragItemsCompleted(TreeView sender, TreeViewDragItemsCompletedEventArgs args)
        {
            if (args.DropResult == DataPackageOperation.Move ) // Check if the drop was successful
            {
                var movedItemData = args.Items.FirstOrDefault();
                // This is the TaskCategory of the node that was dragged and dropped.
                var movedCategory = movedItemData as TaskCategoryViewObject; // Using TaskCategoryViewObject
                var previouseParentCategory = movedCategory?.ParentCategoryKey;
                // This is the TaskCategory of the new parent node where the item was dropped.
                // If dropped at the root level (no parent), this might be null.
                var newParentCategory = args.NewParentItem as TaskCategoryViewObject; // Using TaskCategoryViewObject

                if (movedCategory != null)
                {
                    // SUCCESS: 'movedCategory' is the TaskCategory of the node that was moved.
                    LogService.Instance.Debug($"Drag Completed: Moved '{movedCategory.Name}'");

                    if (newParentCategory != null)
                    {
                        // SUCCESS: 'newParentCategory' is the TaskCategory of the new parent.
                        LogService.Instance.Debug($"   New Parent: '{newParentCategory.Name}'");
                    }
                    else
                    {
                        // The item was dropped at the root level (or NewParentItem is not a TaskCategory).
                        LogService.Instance.Debug("   New Parent: Root level or not a TaskCategory");
                    }

                    IList<TreeViewNode> nodes = null;                    
                    var parentNode = FindNodeByKey(sender, newParentCategory?.Key);
                    if (parentNode != null)
                    {
                        nodes = parentNode.Children;
                    }
                    else
                    {
                        nodes = sender.RootNodes;
                    }
                    
                    var itemsToReposition = new List<RepositionCategoryItem>();

                    for (int i = 0; i < nodes.Count; i++)
                    {
                        var node = nodes[i];
                        var nodeCategory = node.Content as TaskCategoryViewObject;
                        if (nodeCategory != null)
                        {
                            if (!nodeCategory.IsSystemCategory)
                            {
                                itemsToReposition.Add(new RepositionCategoryItem
                                {
                                    CategoryKey = nodeCategory.Key, Position = i
                                });
                            }
                        }
                    }

                    //如果是移动了收集箱或者回收站，则取消并回复之前的状态
                    var first = sender.RootNodes.First().Content as TaskCategoryViewObject;
                    var last = sender.RootNodes.Last().Content as TaskCategoryViewObject;
                    if (first.Name != SpecialTaskListConstants.CollectBoxDisplayName || last.Key != SpecialTaskListConstants.Deleted)
                    {
                        UserCategories.Clear();
                        if (beforeDropedUserCategories != null)
                        {
                            foreach (var item in beforeDropedUserCategories)
                            {
                                UserCategories.Add(item);
                            }
                        }

                        beforeDropedUserCategories = null;
                        return;
                    }
                    
                    var response =
                        await StoreFactoryHolder.getTaskCategoryStore().RepositionCategoriesAsync(UserInfoHolder.getUserId(), itemsToReposition, newParentCategory?.Key,previouseParentCategory);

                    if (response != null)
                    {
                        if (response.Success)
                        {
                            LogService.Instance.Info($"Successfully repositioned categories involving '{movedCategory.Name}'. Backend message: {response.Message}");
                        }
                        else
                        {
                            LogService.Instance.Error($"Failed to reposition categories involving '{movedCategory.Name}'. Backend message: {response.Message}. UI might be inconsistent with backend.");
                            // TODO: Consider notifying the user or attempting to revert the UI change if the backend operation failed critically.
                        }
                    }
                    else
                    {
                        LogService.Instance.Error($"Failed to reposition categories involving '{movedCategory.Name}'. API call returned a null response. UI might be inconsistent with backend.");
                        // TODO: Consider notifying the user or attempting to revert the UI change.
                    }

                  
                }
            }
            else
            {
                // Drag was cancelled or not allowed
                LogService.Instance.Debug($"Drag Completed: Result - {args.DropResult}");
            }
            
            beforeDropedUserCategories = null;
        }


      
        private TaskCategoryViewObject DeepCopyCategory(TaskCategoryViewObject original)
        {
            return TaskCategoryViewObject.DeepCopyCategory(original);
        }
    }
    
   
}