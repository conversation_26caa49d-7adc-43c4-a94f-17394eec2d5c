using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using Windows.System;
using HddtodoUI.Utilities;
using Microsoft.UI.Input;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Input;

namespace HddtodoUI.Controls
{
    public sealed partial class EditableStatusDescriptionControl : UserControl, INotifyPropertyChanged
    {
        private string _statusDescription;
        private string _originalDescription;
        private bool _isEditMode = false;

        public EditableStatusDescriptionControl()
        {
            this.InitializeComponent();
            LoadStatusDescription();
            
            // 鼠标进入时显示编辑按钮
            this.PointerEntered += OnPointerEntered;
            this.PointerExited += OnPointerExited;
        }

        public string StatusDescription
        {
            get => _statusDescription;
            set
            {
                if (_statusDescription != value)
                {
                    _statusDescription = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                if (_isEditMode != value)
                {
                    _isEditMode = value;
                    UpdateVisualState();
                    OnPropertyChanged();
                }
            }
        }

        private void LoadStatusDescription()
        {
            StatusDescription = ConfigSettingsUtils.GetUserStatusDescription();
            if (string.IsNullOrWhiteSpace(StatusDescription))
            {
                StatusDescription = "点击编辑按钮或双击此处\n设置当前状态描述...";
            }
        }

        private void SaveStatusDescription()
        {
            ConfigSettingsUtils.SetUserStatusDescription(StatusDescription);
        }

        private void UpdateVisualState()
        {
            if (IsEditMode)
            {
                DisplayModeGrid.Visibility = Visibility.Collapsed;
                EditModeGrid.Visibility = Visibility.Visible;
                StatusTextBox.Focus(FocusState.Programmatic);
                StatusTextBox.SelectAll();
            }
            else
            {
                DisplayModeGrid.Visibility = Visibility.Visible;
                EditModeGrid.Visibility = Visibility.Collapsed;
            }
        }

        private void OnPointerEntered(object sender, PointerRoutedEventArgs pointerRoutedEventArgs)
        {
            if (!IsEditMode)
            {
                EditButton.Opacity = 1.0;
                HoverBorder.Opacity = 0.3;
            }
        }

        private void OnPointerExited(object sender, PointerRoutedEventArgs pointerRoutedEventArgs)
        {
            if (!IsEditMode)
            {
                EditButton.Opacity = 0.0;
                HoverBorder.Opacity = 0.0;
            }
        }

        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            EnterEditMode();
        }

        private void StatusTextBlock_DoubleTapped(object sender, DoubleTappedRoutedEventArgs e)
        {
            EnterEditMode();
        }

        private void EnterEditMode()
        {
            _originalDescription = StatusDescription;
            IsEditMode = true;
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            SaveChanges();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            CancelChanges();
        }

        private void StatusTextBox_KeyDown(object sender, KeyRoutedEventArgs e)
        {
            // Escape 取消
            if (e.Key == VirtualKey.Escape)
            {
                CancelChanges();
                e.Handled = true;
            }
            // Enter键用于换行，不处理保存
        }

        private void SaveChanges()
        {
            var newDescription = StatusTextBox.Text?.Trim();
            if (string.IsNullOrWhiteSpace(newDescription))
            {
                StatusDescription = "点击编辑按钮或双击此处\n设置当前状态描述...";
            }
            else
            {
                StatusDescription = newDescription;
            }

            SaveStatusDescription();
            IsEditMode = false;
        }

        private void CancelChanges()
        {
            StatusDescription = _originalDescription;
            IsEditMode = false;
        }

        public event PropertyChangedEventHandler PropertyChanged;

        private void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
